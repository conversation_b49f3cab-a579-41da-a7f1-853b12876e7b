{"version": 3, "file": "polyfill.es2018.mjs", "sources": ["../src/stub/symbol.ts", "../src/utils.ts", "../src/lib/helpers/miscellaneous.ts", "../src/lib/helpers/webidl.ts", "../src/lib/simple-queue.ts", "../src/lib/readable-stream/generic-reader.ts", "../src/lib/abstract-ops/internal-methods.ts", "../src/stub/number-isfinite.ts", "../src/stub/math-trunc.ts", "../src/lib/validators/basic.ts", "../src/lib/validators/readable-stream.ts", "../src/lib/readable-stream/default-reader.ts", "../src/target/es2018/stub/async-iterator-prototype.ts", "../src/lib/readable-stream/async-iterator.ts", "../src/stub/number-isnan.ts", "../src/lib/abstract-ops/ecmascript.ts", "../src/lib/abstract-ops/miscellaneous.ts", "../src/lib/abstract-ops/queue-with-sizes.ts", "../src/lib/readable-stream/byte-stream-controller.ts", "../src/lib/readable-stream/byob-reader.ts", "../src/lib/abstract-ops/queuing-strategy.ts", "../src/lib/validators/queuing-strategy.ts", "../src/lib/validators/underlying-sink.ts", "../src/lib/validators/writable-stream.ts", "../src/lib/abort-signal.ts", "../src/lib/writable-stream.ts", "../src/stub/native.ts", "../src/stub/dom-exception.ts", "../src/lib/readable-stream/pipe.ts", "../src/lib/readable-stream/default-controller.ts", "../src/lib/readable-stream/tee.ts", "../src/lib/validators/underlying-source.ts", "../src/lib/validators/reader-options.ts", "../src/lib/validators/iterator-options.ts", "../src/lib/validators/pipe-options.ts", "../src/lib/validators/readable-writable-pair.ts", "../src/lib/readable-stream.ts", "../src/lib/validators/queuing-strategy-init.ts", "../src/lib/byte-length-queuing-strategy.ts", "../src/lib/count-queuing-strategy.ts", "../src/lib/validators/transformer.ts", "../src/lib/transform-stream.ts", "../src/polyfill.ts"], "sourcesContent": ["/// <reference lib=\"es2015.symbol\" />\n\nconst SymbolPolyfill: (description?: string) => symbol =\n  typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ?\n    Symbol :\n    description => `Symbol(${description})` as any as symbol;\n\nexport default SymbolPolyfill;\n", "/// <reference lib=\"dom\" />\n\nexport function noop(): undefined {\n  return undefined;\n}\n\nfunction getGlobals() {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else if (typeof global !== 'undefined') {\n    return global;\n  }\n  return undefined;\n}\n\nexport const globals = getGlobals();\n", "import { noop } from '../../utils';\nimport { AssertionError } from '../../stub/assert';\n\nexport function typeIsObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport const rethrowAssertionErrorRejection: (e: any) => void =\n  DEBUG ? e => {\n    // Used throughout the reference implementation, as `.catch(rethrowAssertionErrorRejection)`, to ensure any errors\n    // get shown. There are places in the spec where we do promise transformations and purposefully ignore or don't\n    // expect any errors, but assertion errors are always problematic.\n    if (e && e instanceof AssertionError) {\n      setTimeout(() => {\n        throw e;\n      }, 0);\n    }\n  } : noop;\n", "import { globals } from '../../utils';\nimport { rethrowAssertionErrorRejection } from './miscellaneous';\nimport assert from '../../stub/assert';\n\nconst originalPromise = Promise;\nconst originalPromiseThen = Promise.prototype.then;\nconst originalPromiseResolve = Promise.resolve.bind(originalPromise);\nconst originalPromiseReject = Promise.reject.bind(originalPromise);\n\nexport function newPromise<T>(executor: (\n  resolve: (value: T | PromiseLike<T>) => void,\n  reject: (reason?: any) => void\n) => void): Promise<T> {\n  return new originalPromise(executor);\n}\n\nexport function promiseResolvedWith<T>(value: T | PromiseLike<T>): Promise<T> {\n  return originalPromiseResolve(value);\n}\n\nexport function promiseRejectedWith<T = never>(reason: any): Promise<T> {\n  return originalPromiseReject(reason);\n}\n\nexport function PerformPromiseThen<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  onRejected?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  // There doesn't appear to be any way to correctly emulate the behaviour from JavaScript, so this is just an\n  // approximation.\n  return originalPromiseThen.call(promise, onFulfilled, onRejected) as Promise<TResult1 | TResult2>;\n}\n\nexport function uponPromise<T>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => void | PromiseLike<void>,\n  onRejected?: (reason: any) => void | PromiseLike<void>): void {\n  PerformPromiseThen(\n    PerformPromiseThen(promise, onFulfilled, onRejected),\n    undefined,\n    rethrowAssertionErrorRejection\n  );\n}\n\nexport function uponFulfillment<T>(promise: Promise<T>, onFulfilled: (value: T) => void | PromiseLike<void>): void {\n  uponPromise(promise, onFulfilled);\n}\n\nexport function uponRejection(promise: Promise<unknown>, onRejected: (reason: any) => void | PromiseLike<void>): void {\n  uponPromise(promise, undefined, onRejected);\n}\n\nexport function transformPromiseWith<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  fulfillmentHandler?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  rejectionHandler?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);\n}\n\nexport function setPromiseIsHandledToTrue(promise: Promise<unknown>): void {\n  PerformPromiseThen(promise, undefined, rethrowAssertionErrorRejection);\n}\n\nexport const queueMicrotask: (fn: () => void) => void = (() => {\n  const globalQueueMicrotask = globals && globals.queueMicrotask;\n  if (typeof globalQueueMicrotask === 'function') {\n    return globalQueueMicrotask;\n  }\n\n  const resolvedPromise = promiseResolvedWith(undefined);\n  return (fn: () => void) => PerformPromiseThen(resolvedPromise, fn);\n})();\n\nexport function reflectCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R, V: T, args: A): R {\n  if (typeof F !== 'function') {\n    throw new TypeError('Argument is not a function');\n  }\n  return Function.prototype.apply.call(F, V, args);\n}\n\nexport function promiseCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R | PromiseLike<R>,\n                                                   V: T,\n                                                   args: A): Promise<R> {\n  assert(typeof F === 'function');\n  assert(V !== undefined);\n  assert(Array.isArray(args));\n  try {\n    return promiseResolvedWith(reflectCall(F, V, args));\n  } catch (value) {\n    return promiseRejectedWith(value);\n  }\n}\n", "import assert from '../stub/assert';\n\n// Original from Chromium\n// https://chromium.googlesource.com/chromium/src/+/0aee4434a4dba42a42abaea9bfbc0cd196a63bc1/third_party/blink/renderer/core/streams/SimpleQueue.js\n\nconst QUEUE_MAX_ARRAY_SIZE = 16384;\n\ninterface Node<T> {\n  _elements: T[];\n  _next: Node<T> | undefined;\n}\n\n/**\n * Simple queue structure.\n *\n * Avoids scalability issues with using a packed array directly by using\n * multiple arrays in a linked list and keeping the array size bounded.\n */\nexport class SimpleQueue<T> {\n  private _front: Node<T>;\n  private _back: Node<T>;\n  private _cursor = 0;\n  private _size = 0;\n\n  constructor() {\n    // _front and _back are always defined.\n    this._front = {\n      _elements: [],\n      _next: undefined\n    };\n    this._back = this._front;\n    // The cursor is used to avoid calling Array.shift().\n    // It contains the index of the front element of the array inside the\n    // front-most node. It is always in the range [0, QUEUE_MAX_ARRAY_SIZE).\n    this._cursor = 0;\n    // When there is only one node, size === elements.length - cursor.\n    this._size = 0;\n  }\n\n  get length(): number {\n    return this._size;\n  }\n\n  // For exception safety, this method is structured in order:\n  // 1. Read state\n  // 2. Calculate required state mutations\n  // 3. Perform state mutations\n  push(element: T): void {\n    const oldBack = this._back;\n    let newBack = oldBack;\n    assert(oldBack._next === undefined);\n    if (oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1) {\n      newBack = {\n        _elements: [],\n        _next: undefined\n      };\n    }\n\n    // push() is the mutation most likely to throw an exception, so it\n    // goes first.\n    oldBack._elements.push(element);\n    if (newBack !== oldBack) {\n      this._back = newBack;\n      oldBack._next = newBack;\n    }\n    ++this._size;\n  }\n\n  // Like push(), shift() follows the read -> calculate -> mutate pattern for\n  // exception safety.\n  shift(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const oldFront = this._front;\n    let newFront = oldFront;\n    const oldCursor = this._cursor;\n    let newCursor = oldCursor + 1;\n\n    const elements = oldFront._elements;\n    const element = elements[oldCursor];\n\n    if (newCursor === QUEUE_MAX_ARRAY_SIZE) {\n      assert(elements.length === QUEUE_MAX_ARRAY_SIZE);\n      assert(oldFront._next !== undefined);\n      newFront = oldFront._next!;\n      newCursor = 0;\n    }\n\n    // No mutations before this point.\n    --this._size;\n    this._cursor = newCursor;\n    if (oldFront !== newFront) {\n      this._front = newFront;\n    }\n\n    // Permit shifted element to be garbage collected.\n    elements[oldCursor] = undefined!;\n\n    return element;\n  }\n\n  // The tricky thing about forEach() is that it can be called\n  // re-entrantly. The queue may be mutated inside the callback. It is easy to\n  // see that push() within the callback has no negative effects since the end\n  // of the queue is checked for on every iteration. If shift() is called\n  // repeatedly within the callback then the next iteration may return an\n  // element that has been removed. In this case the callback will be called\n  // with undefined values until we either \"catch up\" with elements that still\n  // exist or reach the back of the queue.\n  forEach(callback: (element: T) => void): void {\n    let i = this._cursor;\n    let node = this._front;\n    let elements = node._elements;\n    while (i !== elements.length || node._next !== undefined) {\n      if (i === elements.length) {\n        assert(node._next !== undefined);\n        assert(i === QUEUE_MAX_ARRAY_SIZE);\n        node = node._next!;\n        elements = node._elements;\n        i = 0;\n        if (elements.length === 0) {\n          break;\n        }\n      }\n      callback(elements[i]);\n      ++i;\n    }\n  }\n\n  // Return the element that would be returned if shift() was called now,\n  // without modifying the queue.\n  peek(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const front = this._front;\n    const cursor = this._cursor;\n    return front._elements[cursor];\n  }\n}\n", "import assert from '../../stub/assert';\nimport { ReadableStream, ReadableStreamCancel, ReadableStreamReader } from '../readable-stream';\nimport { newPromise, setPromiseIsHandledToTrue } from '../helpers/webidl';\n\nexport function ReadableStreamReaderGenericInitialize<R>(reader: ReadableStreamReader<R>, stream: ReadableStream<R>) {\n  reader._ownerReadableStream = stream;\n  stream._reader = reader;\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseInitialize(reader);\n  } else if (stream._state === 'closed') {\n    defaultReaderClosedPromiseInitializeAsResolved(reader);\n  } else {\n    assert(stream._state === 'errored');\n\n    defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);\n  }\n}\n\n// A client of ReadableStreamDefaultReader and ReadableStreamBYOBReader may use these functions directly to bypass state\n// check.\n\nexport function ReadableStreamReaderGenericCancel(reader: ReadableStreamReader<any>, reason: any): Promise<undefined> {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  return ReadableStreamCancel(stream, reason);\n}\n\nexport function ReadableStreamReaderGenericRelease(reader: ReadableStreamReader<any>) {\n  assert(reader._ownerReadableStream !== undefined);\n  assert(reader._ownerReadableStream._reader === reader);\n\n  if (reader._ownerReadableStream._state === 'readable') {\n    defaultReaderClosedPromiseReject(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  } else {\n    defaultReaderClosedPromiseResetToRejected(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  }\n\n  reader._ownerReadableStream._reader = undefined;\n  reader._ownerReadableStream = undefined!;\n}\n\n// Helper functions for the readers.\n\nexport function readerLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released reader');\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nexport function defaultReaderClosedPromiseInitialize(reader: ReadableStreamReader<any>) {\n  reader._closedPromise = newPromise((resolve, reject) => {\n    reader._closedPromise_resolve = resolve;\n    reader._closedPromise_reject = reject;\n  });\n}\n\nexport function defaultReaderClosedPromiseInitializeAsRejected(reader: ReadableStreamReader<any>, reason: any) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseReject(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseInitializeAsResolved(reader: ReadableStreamReader<any>) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseResolve(reader);\n}\n\nexport function defaultReaderClosedPromiseReject(reader: ReadableStreamReader<any>, reason: any) {\n  if (reader._closedPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(reader._closedPromise);\n  reader._closedPromise_reject(reason);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n\nexport function defaultReaderClosedPromiseResetToRejected(reader: ReadableStreamReader<any>, reason: any) {\n  assert(reader._closedPromise_resolve === undefined);\n  assert(reader._closedPromise_reject === undefined);\n\n  defaultReaderClosedPromiseInitializeAsRejected(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseResolve(reader: ReadableStreamReader<any>) {\n  if (reader._closedPromise_resolve === undefined) {\n    return;\n  }\n\n  reader._closedPromise_resolve(undefined);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n", "export const AbortSteps = Symbol('[[AbortSteps]]');\nexport const ErrorSteps = Symbol('[[ErrorSteps]]');\nexport const CancelSteps = Symbol('[[CancelSteps]]');\nexport const PullSteps = Symbol('[[PullSteps]]');\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isFinite#Polyfill\nconst NumberIsFinite: typeof Number.isFinite = Number.isFinite || function (x) {\n  return typeof x === 'number' && isFinite(x);\n};\n\nexport default NumberIsFinite;\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc#Polyfill\nconst MathTrunc: typeof Math.trunc = Math.trunc || function (v) {\n  return v < 0 ? Math.ceil(v) : Math.floor(v);\n};\n\nexport default MathTrunc;\n", "import NumberIsFinite from '../../stub/number-isfinite';\nimport MathTrunc from '../../stub/math-trunc';\n\n// https://heycam.github.io/webidl/#idl-dictionaries\nexport function isDictionary(x: any): x is object | null {\n  return typeof x === 'object' || typeof x === 'function';\n}\n\nexport function assertDictionary(obj: unknown,\n                                 context: string): asserts obj is object | null | undefined {\n  if (obj !== undefined && !isDictionary(obj)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport type AnyFunction = (...args: any[]) => any;\n\n// https://heycam.github.io/webidl/#idl-callback-functions\nexport function assertFunction(x: unknown, context: string): asserts x is AnyFunction {\n  if (typeof x !== 'function') {\n    throw new TypeError(`${context} is not a function.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-object\nexport function isObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport function assertObject(x: unknown,\n                             context: string): asserts x is object {\n  if (!isObject(x)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport function assertRequiredArgument<T extends any>(x: T | undefined,\n                                                      position: number,\n                                                      context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`Parameter ${position} is required in '${context}'.`);\n  }\n}\n\nexport function assertRequiredField<T extends any>(x: T | undefined,\n                                                   field: string,\n                                                   context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`${field} is required in '${context}'.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-unrestricted-double\nexport function convertUnrestrictedDouble(value: unknown): number {\n  return Number(value);\n}\n\nfunction censorNegativeZero(x: number): number {\n  return x === 0 ? 0 : x;\n}\n\nfunction integerPart(x: number): number {\n  return censorNegativeZero(MathTrunc(x));\n}\n\n// https://heycam.github.io/webidl/#idl-unsigned-long-long\nexport function convertUnsignedLongLongWithEnforceRange(value: unknown, context: string): number {\n  const lowerBound = 0;\n  const upperBound = Number.MAX_SAFE_INTEGER;\n\n  let x = Number(value);\n  x = censorNegativeZero(x);\n\n  if (!NumberIsFinite(x)) {\n    throw new TypeError(`${context} is not a finite number`);\n  }\n\n  x = integerPart(x);\n\n  if (x < lowerBound || x > upperBound) {\n    throw new TypeError(`${context} is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`);\n  }\n\n  if (!NumberIsFinite(x) || x === 0) {\n    return 0;\n  }\n\n  // TODO Use BigInt if supported?\n  // let xBigInt = BigInt(integerPart(x));\n  // xBigInt = BigInt.asUintN(64, xBigInt);\n  // return Number(xBigInt);\n\n  return x;\n}\n", "import { IsReadableStream, ReadableStream } from '../readable-stream';\n\nexport function assertReadableStream(x: unknown, context: string): asserts x is ReadableStream {\n  if (!IsReadableStream(x)) {\n    throw new TypeError(`${context} is not a ReadableStream.`);\n  }\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableStream } from '../readable-stream';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { PullSteps } from '../abstract-ops/internal-methods';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\n\n/**\n * A result returned by {@link ReadableStreamDefaultReader.read}.\n *\n * @public\n */\nexport type ReadableStreamDefaultReadResult<T> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value?: undefined;\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamDefaultReader<R>(stream: ReadableStream): ReadableStreamDefaultReader<R> {\n  return new ReadableStreamDefaultReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadRequest<R>(stream: ReadableStream<R>,\n                                                readRequest: ReadRequest<R>): void {\n  assert(IsReadableStreamDefaultReader(stream._reader));\n  assert(stream._state === 'readable');\n\n  (stream._reader! as ReadableStreamDefaultReader<R>)._readRequests.push(readRequest);\n}\n\nexport function ReadableStreamFulfillReadRequest<R>(stream: ReadableStream<R>, chunk: R | undefined, done: boolean) {\n  const reader = stream._reader as ReadableStreamDefaultReader<R>;\n\n  assert(reader._readRequests.length > 0);\n\n  const readRequest = reader._readRequests.shift()!;\n  if (done) {\n    readRequest._closeSteps();\n  } else {\n    readRequest._chunkSteps(chunk!);\n  }\n}\n\nexport function ReadableStreamGetNumReadRequests<R>(stream: ReadableStream<R>): number {\n  return (stream._reader as ReadableStreamDefaultReader<R>)._readRequests.length;\n}\n\nexport function ReadableStreamHasDefaultReader(stream: ReadableStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamDefaultReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadRequest<R> {\n  _chunkSteps(chunk: R): void;\n\n  _closeSteps(): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A default reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamDefaultReader<R = any> {\n  /** @internal */\n  _ownerReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readRequests: SimpleQueue<ReadRequest<R>>;\n\n  constructor(stream: ReadableStream<R>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamDefaultReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed,\n   * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('read'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: () => resolvePromise({ value: undefined, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamDefaultReaderRead(this, readRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamDefaultReader(this)) {\n      throw defaultReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    if (this._readRequests.length > 0) {\n      throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled');\n    }\n\n    ReadableStreamReaderGenericRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamDefaultReader<R = any>(x: any): x is ReadableStreamDefaultReader<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultReader;\n}\n\nexport function ReadableStreamDefaultReaderRead<R>(reader: ReadableStreamDefaultReader<R>,\n                                                   readRequest: ReadRequest<R>): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    readRequest._closeSteps();\n  } else if (stream._state === 'errored') {\n    readRequest._errorSteps(stream._storedError);\n  } else {\n    assert(stream._state === 'readable');\n    stream._readableStreamController[PullSteps](readRequest as ReadRequest<any>);\n  }\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nfunction defaultReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultReader.prototype.${name} can only be used on a ReadableStreamDefaultReader`);\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\n/* eslint-disable @typescript-eslint/no-empty-function */\nexport const AsyncIteratorPrototype: AsyncIterable<any> | undefined =\n  Object.getPrototypeOf(Object.getPrototypeOf(async function* (): AsyncIterableIterator<any> {}).prototype);\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { ReadableStream } from '../readable-stream';\nimport {\n  AcquireReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  ReadableStreamDefaultReadResult,\n  ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport assert from '../../stub/assert';\nimport { AsyncIteratorPrototype } from '@@target/stub/async-iterator-prototype';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  queueMicrotask,\n  transformPromiseWith\n} from '../helpers/webidl';\n\n/**\n * An async iterator returned by {@link ReadableStream.values}.\n *\n * @public\n */\nexport interface ReadableStreamAsyncIterator<R> extends AsyncIterator<R> {\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nexport class ReadableStreamAsyncIteratorImpl<R> {\n  private readonly _reader: ReadableStreamDefaultReader<R>;\n  private readonly _preventCancel: boolean;\n  private _ongoingPromise: Promise<ReadableStreamDefaultReadResult<R>> | undefined = undefined;\n  private _isFinished = false;\n\n  constructor(reader: ReadableStreamDefaultReader<R>, preventCancel: boolean) {\n    this._reader = reader;\n    this._preventCancel = preventCancel;\n  }\n\n  next(): Promise<ReadableStreamDefaultReadResult<R>> {\n    const nextSteps = () => this._nextSteps();\n    this._ongoingPromise = this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) :\n      nextSteps();\n    return this._ongoingPromise;\n  }\n\n  return(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    const returnSteps = () => this._returnSteps(value);\n    return this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) :\n      returnSteps();\n  }\n\n  private _nextSteps(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value: undefined, done: true });\n    }\n\n    const reader = this._reader;\n    if (reader._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('iterate'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        this._ongoingPromise = undefined;\n        // This needs to be delayed by one microtask, otherwise we stop pulling too early which breaks a test.\n        // FIXME Is this a bug in the specification, or in the test?\n        queueMicrotask(() => resolvePromise({ value: chunk, done: false }));\n      },\n      _closeSteps: () => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        resolvePromise({ value: undefined, done: true });\n      },\n      _errorSteps: reason => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        rejectPromise(reason);\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n    return promise;\n  }\n\n  private _returnSteps(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value, done: true });\n    }\n    this._isFinished = true;\n\n    const reader = this._reader;\n    if (reader._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('finish iterating'));\n    }\n\n    assert(reader._readRequests.length === 0);\n\n    if (!this._preventCancel) {\n      const result = ReadableStreamReaderGenericCancel(reader, value);\n      ReadableStreamReaderGenericRelease(reader);\n      return transformPromiseWith(result, () => ({ value, done: true }));\n    }\n\n    ReadableStreamReaderGenericRelease(reader);\n    return promiseResolvedWith({ value, done: true });\n  }\n}\n\ndeclare class ReadableStreamAsyncIteratorInstance<R> implements ReadableStreamAsyncIterator<R> {\n  /** @interal */\n  _asyncIteratorImpl: ReadableStreamAsyncIteratorImpl<R>;\n\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nconst ReadableStreamAsyncIteratorPrototype: ReadableStreamAsyncIteratorInstance<any> = {\n  next(this: ReadableStreamAsyncIteratorInstance<any>): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('next'));\n    }\n    return this._asyncIteratorImpl.next();\n  },\n\n  return(this: ReadableStreamAsyncIteratorInstance<any>, value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('return'));\n    }\n    return this._asyncIteratorImpl.return(value);\n  }\n} as any;\nif (AsyncIteratorPrototype !== undefined) {\n  Object.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamAsyncIterator<R>(stream: ReadableStream<R>,\n                                                      preventCancel: boolean): ReadableStreamAsyncIterator<R> {\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n  const impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel);\n  const iterator: ReadableStreamAsyncIteratorInstance<R> = Object.create(ReadableStreamAsyncIteratorPrototype);\n  iterator._asyncIteratorImpl = impl;\n  return iterator;\n}\n\nfunction IsReadableStreamAsyncIterator<R = any>(x: any): x is ReadableStreamAsyncIterator<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_asyncIteratorImpl')) {\n    return false;\n  }\n\n  try {\n    // noinspection SuspiciousTypeOfGuard\n    return (x as ReadableStreamAsyncIteratorInstance<any>)._asyncIteratorImpl instanceof\n      ReadableStreamAsyncIteratorImpl;\n  } catch {\n    return false;\n  }\n}\n\n// Helper functions for the ReadableStream.\n\nfunction streamAsyncIteratorBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStreamAsyncIterator.${name} can only be used on a ReadableSteamAsyncIterator`);\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN#Polyfill\nconst NumberIsNaN: typeof Number.isNaN = Number.isNaN || function (x) {\n  // eslint-disable-next-line no-self-compare\n  return x !== x;\n};\n\nexport default NumberIsNaN;\n", "export function CreateArrayFromList<T extends any[]>(elements: T): T {\n  // We use arrays to represent lists, so this is basically a no-op.\n  // Do a slice though just in case we happen to depend on the unique-ness.\n  return elements.slice() as T;\n}\n\nexport function CopyDataBlockBytes(dest: ArrayBuffer,\n                                   destOffset: number,\n                                   src: ArrayBuffer,\n                                   srcOffset: number,\n                                   n: number) {\n  new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);\n}\n\n// Not implemented correctly\nexport function TransferArrayBuffer<T extends ArrayBufferLike>(O: T): T {\n  return O;\n}\n\n// Not implemented correctly\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function CanTransferArrayBuffer(O: ArrayBufferLike): boolean {\n  return true;\n}\n\n// Not implemented correctly\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function IsDetachedBuffer(O: ArrayBufferLike): boolean {\n  return false;\n}\n\nexport function ArrayBufferSlice(buffer: ArrayBufferLike, begin: number, end: number): ArrayBufferLike {\n  // ArrayBuffer.prototype.slice is not available on IE10\n  // https://www.caniuse.com/mdn-javascript_builtins_arraybuffer_slice\n  if (buffer.slice) {\n    return buffer.slice(begin, end);\n  }\n  const length = end - begin;\n  const slice = new ArrayBuffer(length);\n  CopyDataBlockBytes(slice, 0, buffer, begin, length);\n  return slice;\n}\n", "import NumberIsNaN from '../../stub/number-isnan';\nimport { ArrayBufferSlice } from './ecmascript';\n\nexport function IsNonNegativeNumber(v: number): boolean {\n  if (typeof v !== 'number') {\n    return false;\n  }\n\n  if (NumberIsNaN(v)) {\n    return false;\n  }\n\n  if (v < 0) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function CloneAsUint8Array(O: ArrayBufferView): Uint8Array {\n  const buffer = ArrayBufferSlice(O.buffer, O.byteOffset, O.byteOffset + O.byteLength);\n  return new Uint8Array(buffer);\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsNonNegativeNumber } from './miscellaneous';\n\nexport interface QueueContainer<T> {\n  _queue: SimpleQueue<T>;\n  _queueTotalSize: number;\n}\n\nexport interface QueuePair<T> {\n  value: T;\n  size: number;\n}\n\nexport function DequeueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.shift()!;\n  container._queueTotalSize -= pair.size;\n  if (container._queueTotalSize < 0) {\n    container._queueTotalSize = 0;\n  }\n\n  return pair.value;\n}\n\nexport function EnqueueValueWithSize<T>(container: QueueContainer<QueuePair<T>>, value: T, size: number) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  if (!IsNonNegativeNumber(size) || size === Infinity) {\n    throw new RangeError('Size must be a finite, non-NaN, non-negative number.');\n  }\n\n  container._queue.push({ value, size });\n  container._queueTotalSize += size;\n}\n\nexport function PeekQueueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.peek();\n  return pair.value;\n}\n\nexport function ResetQueue<T>(container: QueueContainer<T>) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  container._queue = new SimpleQueue<T>();\n  container._queueTotalSize = 0;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadableStreamHasDefaultReader,\n  ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamAddReadIntoRequest,\n  ReadableStreamFulfillReadIntoRequest,\n  ReadableStreamGetNumReadIntoRequests,\n  ReadableStreamHasBYOBReader,\n  ReadIntoRequest\n} from './byob-reader';\nimport NumberIsInteger from '../../stub/number-isinteger';\nimport {\n  IsReadableStreamLocked,\n  ReadableByteStream,\n  ReadableStreamClose,\n  ReadableStreamError\n} from '../readable-stream';\nimport { ValidatedUnderlyingByteSource } from './underlying-source';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  <PERSON>rrayBufferSlice,\n  CanTransferArrayBuffer,\n  CopyDataBlockBytes,\n  IsDetachedBuffer,\n  TransferArrayBuffer\n} from '../abstract-ops/ecmascript';\nimport { CancelSteps, PullSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\nimport { assertRequiredArgument, convertUnsignedLongLongWithEnforceRange } from '../validators/basic';\n\n/**\n * A pull-into request in a {@link ReadableByteStreamController}.\n *\n * @public\n */\nexport class ReadableStreamBYOBRequest {\n  /** @internal */\n  _associatedReadableByteStreamController!: ReadableByteStreamController;\n  /** @internal */\n  _view!: ArrayBufferView | null;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.\n   */\n  get view(): ArrayBufferView | null {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('view');\n    }\n\n    return this._view;\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that `bytesWritten` bytes were written into\n   * {@link ReadableStreamBYOBRequest.view | view}, causing the result be surfaced to the consumer.\n   *\n   * After this method is called, {@link ReadableStreamBYOBRequest.view | view} will be transferred and no longer\n   * modifiable.\n   */\n  respond(bytesWritten: number): void;\n  respond(bytesWritten: number | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respond');\n    }\n    assertRequiredArgument(bytesWritten, 1, 'respond');\n    bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, 'First parameter');\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(this._view!.buffer)) {\n      throw new TypeError(`The BYOB request's buffer has been detached and so cannot be used as a response`);\n    }\n\n    assert(this._view!.byteLength > 0);\n    assert(this._view!.buffer.byteLength > 0);\n\n    ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that instead of writing into\n   * {@link ReadableStreamBYOBRequest.view | view}, the underlying byte source is providing a new `ArrayBufferView`,\n   * which will be given to the consumer of the readable byte stream.\n   *\n   * After this method is called, `view` will be transferred and no longer modifiable.\n   */\n  respondWithNewView(view: ArrayBufferView): void;\n  respondWithNewView(view: ArrayBufferView | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respondWithNewView');\n    }\n    assertRequiredArgument(view, 1, 'respondWithNewView');\n\n    if (!ArrayBuffer.isView(view)) {\n      throw new TypeError('You can only respond with array buffer views');\n    }\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(view.buffer)) {\n      throw new TypeError('The given view\\'s buffer has been detached and so cannot be used as a response');\n    }\n\n    ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n  respond: { enumerable: true },\n  respondWithNewView: { enumerable: true },\n  view: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBRequest.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBRequest',\n    configurable: true\n  });\n}\n\ninterface ArrayBufferViewConstructor<T extends ArrayBufferView = ArrayBufferView> {\n  new(buffer: ArrayBufferLike, byteOffset: number, length?: number): T;\n\n  readonly prototype: T;\n  readonly BYTES_PER_ELEMENT: number;\n}\n\ninterface ByteQueueElement {\n  buffer: ArrayBufferLike;\n  byteOffset: number;\n  byteLength: number;\n}\n\ntype PullIntoDescriptor<T extends ArrayBufferView = ArrayBufferView> =\n  DefaultPullIntoDescriptor\n  | BYOBPullIntoDescriptor<T>;\n\ninterface DefaultPullIntoDescriptor {\n  buffer: ArrayBufferLike;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<Uint8Array>;\n  readerType: 'default';\n}\n\ninterface BYOBPullIntoDescriptor<T extends ArrayBufferView = ArrayBufferView> {\n  buffer: ArrayBufferLike;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<T>;\n  readerType: 'byob';\n}\n\n/**\n * Allows control of a {@link ReadableStream | readable byte stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableByteStreamController {\n  /** @internal */\n  _controlledReadableByteStream!: ReadableByteStream;\n  /** @internal */\n  _queue!: SimpleQueue<ByteQueueElement>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n  /** @internal */\n  _autoAllocateChunkSize: number | undefined;\n  /** @internal */\n  _byobRequest: ReadableStreamBYOBRequest | null;\n  /** @internal */\n  _pendingPullIntos!: SimpleQueue<PullIntoDescriptor>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the current BYOB pull request, or `null` if there isn't one.\n   */\n  get byobRequest(): ReadableStreamBYOBRequest | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('byobRequest');\n    }\n\n    return ReadableByteStreamControllerGetBYOBRequest(this);\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableByteStreamControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('close');\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('The stream has already been closed; do not close it again!');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be closed`);\n    }\n\n    ReadableByteStreamControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk chunk in the controlled readable stream.\n   * The chunk has to be an `ArrayBufferView` instance, or else a `TypeError` will be thrown.\n   */\n  enqueue(chunk: ArrayBufferView): void;\n  enqueue(chunk: ArrayBufferView | undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('enqueue');\n    }\n\n    assertRequiredArgument(chunk, 1, 'enqueue');\n    if (!ArrayBuffer.isView(chunk)) {\n      throw new TypeError('chunk must be an array buffer view');\n    }\n    if (chunk.byteLength === 0) {\n      throw new TypeError('chunk must have non-zero byteLength');\n    }\n    if (chunk.buffer.byteLength === 0) {\n      throw new TypeError(`chunk's buffer must have non-zero byteLength`);\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('stream is closed or draining');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be enqueued to`);\n    }\n\n    ReadableByteStreamControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('error');\n    }\n\n    ReadableByteStreamControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ReadableByteStreamControllerClearPendingPullIntos(this);\n\n    ResetQueue(this);\n\n    const result = this._cancelAlgorithm(reason);\n    ReadableByteStreamControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<Uint8Array>): void {\n    const stream = this._controlledReadableByteStream;\n    assert(ReadableStreamHasDefaultReader(stream));\n\n    if (this._queueTotalSize > 0) {\n      assert(ReadableStreamGetNumReadRequests(stream) === 0);\n\n      const entry = this._queue.shift()!;\n      this._queueTotalSize -= entry.byteLength;\n\n      ReadableByteStreamControllerHandleQueueDrain(this);\n\n      const view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);\n\n      readRequest._chunkSteps(view);\n      return;\n    }\n\n    const autoAllocateChunkSize = this._autoAllocateChunkSize;\n    if (autoAllocateChunkSize !== undefined) {\n      let buffer: ArrayBuffer;\n      try {\n        buffer = new ArrayBuffer(autoAllocateChunkSize);\n      } catch (bufferE) {\n        readRequest._errorSteps(bufferE);\n        return;\n      }\n\n      const pullIntoDescriptor: DefaultPullIntoDescriptor = {\n        buffer,\n        bufferByteLength: autoAllocateChunkSize,\n        byteOffset: 0,\n        byteLength: autoAllocateChunkSize,\n        bytesFilled: 0,\n        elementSize: 1,\n        viewConstructor: Uint8Array,\n        readerType: 'default'\n      };\n\n      this._pendingPullIntos.push(pullIntoDescriptor);\n    }\n\n    ReadableStreamAddReadRequest(stream, readRequest);\n    ReadableByteStreamControllerCallPullIfNeeded(this);\n  }\n}\n\nObject.defineProperties(ReadableByteStreamController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  byobRequest: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableByteStreamController.prototype, Symbol.toStringTag, {\n    value: 'ReadableByteStreamController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableByteStreamController.\n\nexport function IsReadableByteStreamController(x: any): x is ReadableByteStreamController {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableByteStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableByteStreamController;\n}\n\nfunction IsReadableStreamBYOBRequest(x: any): x is ReadableStreamBYOBRequest {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_associatedReadableByteStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBRequest;\n}\n\nfunction ReadableByteStreamControllerCallPullIfNeeded(controller: ReadableByteStreamController): void {\n  const shouldPull = ReadableByteStreamControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  // TODO: Test controller argument\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n      }\n    },\n    e => {\n      ReadableByteStreamControllerError(controller, e);\n    }\n  );\n}\n\nfunction ReadableByteStreamControllerClearPendingPullIntos(controller: ReadableByteStreamController) {\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  controller._pendingPullIntos = new SimpleQueue();\n}\n\nfunction ReadableByteStreamControllerCommitPullIntoDescriptor<T extends ArrayBufferView>(\n  stream: ReadableByteStream,\n  pullIntoDescriptor: PullIntoDescriptor<T>\n) {\n  assert(stream._state !== 'errored');\n\n  let done = false;\n  if (stream._state === 'closed') {\n    assert(pullIntoDescriptor.bytesFilled === 0);\n    done = true;\n  }\n\n  const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n  if (pullIntoDescriptor.readerType === 'default') {\n    ReadableStreamFulfillReadRequest(stream, filledView as unknown as Uint8Array, done);\n  } else {\n    assert(pullIntoDescriptor.readerType === 'byob');\n    ReadableStreamFulfillReadIntoRequest(stream, filledView, done);\n  }\n}\n\nfunction ReadableByteStreamControllerConvertPullIntoDescriptor<T extends ArrayBufferView>(\n  pullIntoDescriptor: PullIntoDescriptor<T>\n): T {\n  const bytesFilled = pullIntoDescriptor.bytesFilled;\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  assert(bytesFilled <= pullIntoDescriptor.byteLength);\n  assert(bytesFilled % elementSize === 0);\n\n  return new pullIntoDescriptor.viewConstructor(\n    pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize) as T;\n}\n\nfunction ReadableByteStreamControllerEnqueueChunkToQueue(controller: ReadableByteStreamController,\n                                                         buffer: ArrayBufferLike,\n                                                         byteOffset: number,\n                                                         byteLength: number) {\n  controller._queue.push({ buffer, byteOffset, byteLength });\n  controller._queueTotalSize += byteLength;\n}\n\nfunction ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller: ReadableByteStreamController,\n                                                                     pullIntoDescriptor: PullIntoDescriptor) {\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  const currentAlignedBytes = pullIntoDescriptor.bytesFilled - pullIntoDescriptor.bytesFilled % elementSize;\n\n  const maxBytesToCopy = Math.min(controller._queueTotalSize,\n                                  pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled);\n  const maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy;\n  const maxAlignedBytes = maxBytesFilled - maxBytesFilled % elementSize;\n\n  let totalBytesToCopyRemaining = maxBytesToCopy;\n  let ready = false;\n  if (maxAlignedBytes > currentAlignedBytes) {\n    totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled;\n    ready = true;\n  }\n\n  const queue = controller._queue;\n\n  while (totalBytesToCopyRemaining > 0) {\n    const headOfQueue = queue.peek();\n\n    const bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength);\n\n    const destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy);\n\n    if (headOfQueue.byteLength === bytesToCopy) {\n      queue.shift();\n    } else {\n      headOfQueue.byteOffset += bytesToCopy;\n      headOfQueue.byteLength -= bytesToCopy;\n    }\n    controller._queueTotalSize -= bytesToCopy;\n\n    ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor);\n\n    totalBytesToCopyRemaining -= bytesToCopy;\n  }\n\n  if (!ready) {\n    assert(controller._queueTotalSize === 0);\n    assert(pullIntoDescriptor.bytesFilled > 0);\n    assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.elementSize);\n  }\n\n  return ready;\n}\n\nfunction ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller: ReadableByteStreamController,\n                                                                size: number,\n                                                                pullIntoDescriptor: PullIntoDescriptor) {\n  assert(controller._pendingPullIntos.length === 0 || controller._pendingPullIntos.peek() === pullIntoDescriptor);\n  assert(controller._byobRequest === null);\n  pullIntoDescriptor.bytesFilled += size;\n}\n\nfunction ReadableByteStreamControllerHandleQueueDrain(controller: ReadableByteStreamController) {\n  assert(controller._controlledReadableByteStream._state === 'readable');\n\n  if (controller._queueTotalSize === 0 && controller._closeRequested) {\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamClose(controller._controlledReadableByteStream);\n  } else {\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n  }\n}\n\nfunction ReadableByteStreamControllerInvalidateBYOBRequest(controller: ReadableByteStreamController) {\n  if (controller._byobRequest === null) {\n    return;\n  }\n\n  controller._byobRequest._associatedReadableByteStreamController = undefined!;\n  controller._byobRequest._view = null!;\n  controller._byobRequest = null;\n}\n\nfunction ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller: ReadableByteStreamController) {\n  assert(!controller._closeRequested);\n\n  while (controller._pendingPullIntos.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n\n    const pullIntoDescriptor = controller._pendingPullIntos.peek();\n\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n      ReadableByteStreamControllerCommitPullIntoDescriptor(\n        controller._controlledReadableByteStream,\n        pullIntoDescriptor\n      );\n    }\n  }\n}\n\nexport function ReadableByteStreamControllerPullInto<T extends ArrayBufferView>(\n  controller: ReadableByteStreamController,\n  view: T,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = controller._controlledReadableByteStream;\n\n  let elementSize = 1;\n  if (view.constructor !== DataView) {\n    elementSize = (view.constructor as ArrayBufferViewConstructor<T>).BYTES_PER_ELEMENT;\n  }\n\n  const ctor = view.constructor as ArrayBufferViewConstructor<T>;\n\n  // try {\n  const buffer = TransferArrayBuffer(view.buffer);\n  // } catch (e) {\n  //   readIntoRequest._errorSteps(e);\n  //   return;\n  // }\n\n  const pullIntoDescriptor: BYOBPullIntoDescriptor<T> = {\n    buffer,\n    bufferByteLength: buffer.byteLength,\n    byteOffset: view.byteOffset,\n    byteLength: view.byteLength,\n    bytesFilled: 0,\n    elementSize,\n    viewConstructor: ctor,\n    readerType: 'byob'\n  };\n\n  if (controller._pendingPullIntos.length > 0) {\n    controller._pendingPullIntos.push(pullIntoDescriptor);\n\n    // No ReadableByteStreamControllerCallPullIfNeeded() call since:\n    // - No change happens on desiredSize\n    // - The source has already been notified of that there's at least 1 pending read(view)\n\n    ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n    return;\n  }\n\n  if (stream._state === 'closed') {\n    const emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);\n    readIntoRequest._closeSteps(emptyView);\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n\n      ReadableByteStreamControllerHandleQueueDrain(controller);\n\n      readIntoRequest._chunkSteps(filledView);\n      return;\n    }\n\n    if (controller._closeRequested) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      readIntoRequest._errorSteps(e);\n      return;\n    }\n  }\n\n  controller._pendingPullIntos.push(pullIntoDescriptor);\n\n  ReadableStreamAddReadIntoRequest<T>(stream, readIntoRequest);\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInClosedState(controller: ReadableByteStreamController,\n                                                          firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.bytesFilled === 0);\n\n  const stream = controller._controlledReadableByteStream;\n  if (ReadableStreamHasBYOBReader(stream)) {\n    while (ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n      const pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);\n      ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerRespondInReadableState(controller: ReadableByteStreamController,\n                                                            bytesWritten: number,\n                                                            pullIntoDescriptor: PullIntoDescriptor) {\n  assert(pullIntoDescriptor.bytesFilled + bytesWritten <= pullIntoDescriptor.byteLength);\n\n  ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor);\n\n  if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.elementSize) {\n    return;\n  }\n\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n  const remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;\n  if (remainderSize > 0) {\n    const end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    const remainder = ArrayBufferSlice(pullIntoDescriptor.buffer, end - remainderSize, end);\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, remainder, 0, remainder.byteLength);\n  }\n\n  pullIntoDescriptor.bytesFilled -= remainderSize;\n  ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n\n  ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInternal(controller: ReadableByteStreamController, bytesWritten: number) {\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  assert(CanTransferArrayBuffer(firstDescriptor.buffer));\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n\n  const state = controller._controlledReadableByteStream._state;\n  if (state === 'closed') {\n    assert(bytesWritten === 0);\n    ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor);\n  } else {\n    assert(state === 'readable');\n    assert(bytesWritten > 0);\n    ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerShiftPendingPullInto(\n  controller: ReadableByteStreamController\n): PullIntoDescriptor {\n  assert(controller._byobRequest === null);\n  const descriptor = controller._pendingPullIntos.shift()!;\n  return descriptor;\n}\n\nfunction ReadableByteStreamControllerShouldCallPull(controller: ReadableByteStreamController): boolean {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return false;\n  }\n\n  if (controller._closeRequested) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  if (ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableByteStreamControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableByteStreamControllerClearAlgorithms(controller: ReadableByteStreamController) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\n// A client of ReadableByteStreamController may use these functions directly to bypass state check.\n\nexport function ReadableByteStreamControllerClose(controller: ReadableByteStreamController) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    controller._closeRequested = true;\n\n    return;\n  }\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (firstPendingPullInto.bytesFilled > 0) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      throw e;\n    }\n  }\n\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamClose(stream);\n}\n\nexport function ReadableByteStreamControllerEnqueue(controller: ReadableByteStreamController, chunk: ArrayBufferView) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  const buffer = chunk.buffer;\n  const byteOffset = chunk.byteOffset;\n  const byteLength = chunk.byteLength;\n  if (IsDetachedBuffer(buffer)) {\n    throw new TypeError('chunk\\'s buffer is detached and so cannot be enqueued');\n  }\n  const transferredBuffer = TransferArrayBuffer(buffer);\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (IsDetachedBuffer(firstPendingPullInto.buffer)) {\n      throw new TypeError(\n        'The BYOB request\\'s buffer has been detached and so cannot be filled with an enqueued chunk'\n      );\n    }\n    firstPendingPullInto.buffer = TransferArrayBuffer(firstPendingPullInto.buffer);\n  }\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n\n  if (ReadableStreamHasDefaultReader(stream)) {\n    if (ReadableStreamGetNumReadRequests(stream) === 0) {\n      assert(controller._pendingPullIntos.length === 0);\n      ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    } else {\n      assert(controller._queue.length === 0);\n      if (controller._pendingPullIntos.length > 0) {\n        assert(controller._pendingPullIntos.peek().readerType === 'default');\n        ReadableByteStreamControllerShiftPendingPullInto(controller);\n      }\n      const transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);\n      ReadableStreamFulfillReadRequest(stream, transferredView, false);\n    }\n  } else if (ReadableStreamHasBYOBReader(stream)) {\n    // TODO: Ideally in this branch detaching should happen only if the buffer is not consumed fully.\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n  } else {\n    assert(!IsReadableStreamLocked(stream));\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableByteStreamControllerError(controller: ReadableByteStreamController, e: any) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ReadableByteStreamControllerClearPendingPullIntos(controller);\n\n  ResetQueue(controller);\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableByteStreamControllerGetBYOBRequest(\n  controller: ReadableByteStreamController\n): ReadableStreamBYOBRequest | null {\n  if (controller._byobRequest === null && controller._pendingPullIntos.length > 0) {\n    const firstDescriptor = controller._pendingPullIntos.peek();\n    const view = new Uint8Array(firstDescriptor.buffer,\n                                firstDescriptor.byteOffset + firstDescriptor.bytesFilled,\n                                firstDescriptor.byteLength - firstDescriptor.bytesFilled);\n\n    const byobRequest: ReadableStreamBYOBRequest = Object.create(ReadableStreamBYOBRequest.prototype);\n    SetUpReadableStreamBYOBRequest(byobRequest, controller, view);\n    controller._byobRequest = byobRequest;\n  }\n  return controller._byobRequest;\n}\n\nfunction ReadableByteStreamControllerGetDesiredSize(controller: ReadableByteStreamController): number | null {\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nexport function ReadableByteStreamControllerRespond(controller: ReadableByteStreamController, bytesWritten: number) {\n  assert(controller._pendingPullIntos.length > 0);\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (bytesWritten !== 0) {\n      throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (bytesWritten === 0) {\n      throw new TypeError('bytesWritten must be greater than 0 when calling respond() on a readable stream');\n    }\n    if (firstDescriptor.bytesFilled + bytesWritten > firstDescriptor.byteLength) {\n      throw new RangeError('bytesWritten out of range');\n    }\n  }\n\n  firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer);\n\n  ReadableByteStreamControllerRespondInternal(controller, bytesWritten);\n}\n\nexport function ReadableByteStreamControllerRespondWithNewView(controller: ReadableByteStreamController,\n                                                               view: ArrayBufferView) {\n  assert(controller._pendingPullIntos.length > 0);\n  assert(!IsDetachedBuffer(view.buffer));\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (view.byteLength !== 0) {\n      throw new TypeError('The view\\'s length must be 0 when calling respondWithNewView() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (view.byteLength === 0) {\n      throw new TypeError(\n        'The view\\'s length must be greater than 0 when calling respondWithNewView() on a readable stream'\n      );\n    }\n  }\n\n  if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset) {\n    throw new RangeError('The region specified by view does not match byobRequest');\n  }\n  if (firstDescriptor.bufferByteLength !== view.buffer.byteLength) {\n    throw new RangeError('The buffer of view has different capacity than byobRequest');\n  }\n  if (firstDescriptor.bytesFilled + view.byteLength > firstDescriptor.byteLength) {\n    throw new RangeError('The region specified by view is larger than byobRequest');\n  }\n\n  const viewByteLength = view.byteLength;\n  firstDescriptor.buffer = TransferArrayBuffer(view.buffer);\n  ReadableByteStreamControllerRespondInternal(controller, viewByteLength);\n}\n\nexport function SetUpReadableByteStreamController(stream: ReadableByteStream,\n                                                  controller: ReadableByteStreamController,\n                                                  startAlgorithm: () => void | PromiseLike<void>,\n                                                  pullAlgorithm: () => Promise<void>,\n                                                  cancelAlgorithm: (reason: any) => Promise<void>,\n                                                  highWaterMark: number,\n                                                  autoAllocateChunkSize: number | undefined) {\n  assert(stream._readableStreamController === undefined);\n  if (autoAllocateChunkSize !== undefined) {\n    assert(NumberIsInteger(autoAllocateChunkSize));\n    assert(autoAllocateChunkSize > 0);\n  }\n\n  controller._controlledReadableByteStream = stream;\n\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._byobRequest = null;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._closeRequested = false;\n  controller._started = false;\n\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._autoAllocateChunkSize = autoAllocateChunkSize;\n\n  controller._pendingPullIntos = new SimpleQueue();\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableByteStreamControllerCallPullIfNeeded(controller);\n    },\n    r => {\n      ReadableByteStreamControllerError(controller, r);\n    }\n  );\n}\n\nexport function SetUpReadableByteStreamControllerFromUnderlyingSource(\n  stream: ReadableByteStream,\n  underlyingByteSource: ValidatedUnderlyingByteSource,\n  highWaterMark: number\n) {\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let pullAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let cancelAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingByteSource.start !== undefined) {\n    startAlgorithm = () => underlyingByteSource.start!(controller);\n  }\n  if (underlyingByteSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingByteSource.pull!(controller);\n  }\n  if (underlyingByteSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingByteSource.cancel!(reason);\n  }\n\n  const autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;\n  if (autoAllocateChunkSize === 0) {\n    throw new TypeError('autoAllocateChunkSize must be greater than 0');\n  }\n\n  SetUpReadableByteStreamController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize\n  );\n}\n\nfunction SetUpReadableStreamBYOBRequest(request: ReadableStreamBYOBRequest,\n                                        controller: ReadableByteStreamController,\n                                        view: ArrayBufferView) {\n  assert(IsReadableByteStreamController(controller));\n  assert(typeof view === 'object');\n  assert(ArrayBuffer.isView(view));\n  assert(!IsDetachedBuffer(view.buffer));\n  request._associatedReadableByteStreamController = controller;\n  request._view = view;\n}\n\n// Helper functions for the ReadableStreamBYOBRequest.\n\nfunction byobRequestBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBRequest.prototype.${name} can only be used on a ReadableStreamBYOBRequest`);\n}\n\n// Helper functions for the ReadableByteStreamController.\n\nfunction byteStreamControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableByteStreamController.prototype.${name} can only be used on a ReadableByteStreamController`);\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableByteStream, ReadableStream } from '../readable-stream';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamController,\n  ReadableByteStreamControllerPullInto\n} from './byte-stream-controller';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\nimport { IsDetachedBuffer } from '../abstract-ops/ecmascript';\n\n/**\n * A result returned by {@link ReadableStreamBYOBReader.read}.\n *\n * @public\n */\nexport type ReadableStreamBYOBReadResult<T extends ArrayBufferView> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value: T | undefined;\n};\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamBYOBReader(stream: ReadableByteStream): ReadableStreamBYOBReader {\n  return new ReadableStreamBYOBReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadIntoRequest<T extends ArrayBufferView>(stream: ReadableByteStream,\n                                                                            readIntoRequest: ReadIntoRequest<T>): void {\n  assert(IsReadableStreamBYOBReader(stream._reader));\n  assert(stream._state === 'readable' || stream._state === 'closed');\n\n  (stream._reader! as ReadableStreamBYOBReader)._readIntoRequests.push(readIntoRequest);\n}\n\nexport function ReadableStreamFulfillReadIntoRequest(stream: ReadableByteStream,\n                                                     chunk: ArrayBufferView,\n                                                     done: boolean) {\n  const reader = stream._reader as ReadableStreamBYOBReader;\n\n  assert(reader._readIntoRequests.length > 0);\n\n  const readIntoRequest = reader._readIntoRequests.shift()!;\n  if (done) {\n    readIntoRequest._closeSteps(chunk);\n  } else {\n    readIntoRequest._chunkSteps(chunk);\n  }\n}\n\nexport function ReadableStreamGetNumReadIntoRequests(stream: ReadableByteStream): number {\n  return (stream._reader as ReadableStreamBYOBReader)._readIntoRequests.length;\n}\n\nexport function ReadableStreamHasBYOBReader(stream: ReadableByteStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamBYOBReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadIntoRequest<T extends ArrayBufferView> {\n  _chunkSteps(chunk: T): void;\n\n  _closeSteps(chunk: T | undefined): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A BYOB reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamBYOBReader {\n  /** @internal */\n  _ownerReadableStream!: ReadableByteStream;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readIntoRequests: SimpleQueue<ReadIntoRequest<any>>;\n\n  constructor(stream: ReadableByteStream) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamBYOBReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    if (!IsReadableByteStreamController(stream._readableStreamController)) {\n      throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte ' +\n        'source');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readIntoRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Attempts to reads bytes into view, and returns a promise resolved with the result.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read<T extends ArrayBufferView>(view: T): Promise<ReadableStreamBYOBReadResult<T>> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('read'));\n    }\n\n    if (!ArrayBuffer.isView(view)) {\n      return promiseRejectedWith(new TypeError('view must be an array buffer view'));\n    }\n    if (view.byteLength === 0) {\n      return promiseRejectedWith(new TypeError('view must have non-zero byteLength'));\n    }\n    if (view.buffer.byteLength === 0) {\n      return promiseRejectedWith(new TypeError(`view's buffer must have non-zero byteLength`));\n    }\n    if (IsDetachedBuffer(view.buffer)) {\n      return promiseRejectedWith(new TypeError('view\\'s buffer has been detached'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamBYOBReadResult<T>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamBYOBReadResult<T>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readIntoRequest: ReadIntoRequest<T> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: chunk => resolvePromise({ value: chunk, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamBYOBReaderRead(this, view, readIntoRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamBYOBReader(this)) {\n      throw byobReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    if (this._readIntoRequests.length > 0) {\n      throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled');\n    }\n\n    ReadableStreamReaderGenericRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamBYOBReader(x: any): x is ReadableStreamBYOBReader {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readIntoRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBReader;\n}\n\nexport function ReadableStreamBYOBReaderRead<T extends ArrayBufferView>(\n  reader: ReadableStreamBYOBReader,\n  view: T,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'errored') {\n    readIntoRequest._errorSteps(stream._storedError);\n  } else {\n    ReadableByteStreamControllerPullInto(\n      stream._readableStreamController as ReadableByteStreamController,\n      view,\n      readIntoRequest\n    );\n  }\n}\n\n// Helper functions for the ReadableStreamBYOBReader.\n\nfunction byobReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBReader.prototype.${name} can only be used on a ReadableStreamBYOBReader`);\n}\n", "import { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport NumberIsNaN from '../../stub/number-isnan';\n\nexport function ExtractHighWaterMark(strategy: QueuingStrategy, defaultHWM: number): number {\n  const { highWaterMark } = strategy;\n\n  if (highWaterMark === undefined) {\n    return defaultHWM;\n  }\n\n  if (NumberIsNaN(highWaterMark) || highWaterMark < 0) {\n    throw new RangeError('Invalid highWaterMark');\n  }\n\n  return highWaterMark;\n}\n\nexport function ExtractSizeAlgorithm<T>(strategy: QueuingStrategy<T>): QueuingStrategySizeCallback<T> {\n  const { size } = strategy;\n\n  if (!size) {\n    return () => 1;\n  }\n\n  return size;\n}\n", "import { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport { assertDictionary, assertFunction, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategy<T>(init: QueuingStrategy<T> | null | undefined,\n                                          context: string): QueuingStrategy<T> {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  const size = init?.size;\n  return {\n    highWaterMark: highWaterMark === undefined ? undefined : convertUnrestrictedDouble(highWaterMark),\n    size: size === undefined ? undefined : convertQueuingStrategySize(size, `${context} has member 'size' that`)\n  };\n}\n\nfunction convertQueuingStrategySize<T>(fn: QueuingStrategySizeCallback<T>,\n                                       context: string): QueuingStrategySizeCallback<T> {\n  assertFunction(fn, context);\n  return chunk => convertUnrestrictedDouble(fn(chunk));\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from '../writable-stream/underlying-sink';\nimport { WritableStreamDefaultController } from '../writable-stream';\n\nexport function convertUnderlyingSink<W>(original: UnderlyingSink<W> | null,\n                                         context: string): ValidatedUnderlyingSink<W> {\n  assertDictionary(original, context);\n  const abort = original?.abort;\n  const close = original?.close;\n  const start = original?.start;\n  const type = original?.type;\n  const write = original?.write;\n  return {\n    abort: abort === undefined ?\n      undefined :\n      convertUnderlyingSinkAbortCallback(abort, original!, `${context} has member 'abort' that`),\n    close: close === undefined ?\n      undefined :\n      convertUnderlyingSinkCloseCallback(close, original!, `${context} has member 'close' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSinkStartCallback(start, original!, `${context} has member 'start' that`),\n    write: write === undefined ?\n      undefined :\n      convertUnderlyingSinkWriteCallback(write, original!, `${context} has member 'write' that`),\n    type\n  };\n}\n\nfunction convertUnderlyingSinkAbortCallback(\n  fn: UnderlyingSinkAbortCallback,\n  original: UnderlyingSink,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSinkCloseCallback(\n  fn: UnderlyingSinkCloseCallback,\n  original: UnderlyingSink,\n  context: string\n): () => Promise<void> {\n  assertFunction(fn, context);\n  return () => promiseCall(fn, original, []);\n}\n\nfunction convertUnderlyingSinkStartCallback(\n  fn: UnderlyingSinkStartCallback,\n  original: UnderlyingSink,\n  context: string\n): UnderlyingSinkStartCallback {\n  assertFunction(fn, context);\n  return (controller: WritableStreamDefaultController) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSinkWriteCallback<W>(\n  fn: UnderlyingSinkWriteCallback<W>,\n  original: UnderlyingSink<W>,\n  context: string\n): (chunk: W, controller: WritableStreamDefaultController) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: W, controller: WritableStreamDefaultController) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import { IsWritableStream, WritableStream } from '../writable-stream';\n\nexport function assertWritableStream(x: unknown, context: string): asserts x is WritableStream {\n  if (!IsWritableStream(x)) {\n    throw new TypeError(`${context} is not a WritableStream.`);\n  }\n}\n", "/**\n * A signal object that allows you to communicate with a request and abort it if required\n * via its associated `AbortController` object.\n *\n * @remarks\n *   This interface is compatible with the `AbortSignal` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @public\n */\nexport interface AbortSignal {\n  /**\n   * Whether the request is aborted.\n   */\n  readonly aborted: boolean;\n\n  /**\n   * Add an event listener to be triggered when this signal becomes aborted.\n   */\n  addEventListener(type: 'abort', listener: () => void): void;\n\n  /**\n   * Remove an event listener that was previously added with {@link AbortSignal.addEventListener}.\n   */\n  removeEventListener(type: 'abort', listener: () => void): void;\n}\n\nexport function isAbortSignal(value: unknown): value is AbortSignal {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  try {\n    return typeof (value as AbortSignal).aborted === 'boolean';\n  } catch {\n    // AbortSignal.prototype.aborted throws if its brand check fails\n    return false;\n  }\n}\n\n/**\n * A controller object that allows you to abort an `AbortSignal` when desired.\n *\n * @remarks\n *   This interface is compatible with the `AbortController` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @internal\n */\nexport interface AbortController {\n  readonly signal: AbortSignal;\n\n  abort(): void;\n}\n\ninterface AbortControllerConstructor {\n  new(): AbortController;\n}\n\nconst supportsAbortController = typeof (AbortController as any) === 'function';\n\n/**\n * Construct a new AbortController, if supported by the platform.\n *\n * @internal\n */\nexport function createAbortController(): AbortController | undefined {\n  if (supportsAbortController) {\n    return new (AbortController as AbortControllerConstructor)();\n  }\n  return undefined;\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponPromise\n} from './helpers/webidl';\nimport {\n  DequeueValue,\n  EnqueueValueWithSize,\n  PeekQueueValue,\n  QueuePair,\n  ResetQueue\n} from './abstract-ops/queue-with-sizes';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { SimpleQueue } from './simple-queue';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { AbortSteps, ErrorSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from './writable-stream/underlying-sink';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertUnderlyingSink } from './validators/underlying-sink';\nimport { assertWritableStream } from './validators/writable-stream';\nimport { AbortController, AbortSignal, createAbortController } from './abort-signal';\n\ntype WritableStreamState = 'writable' | 'closed' | 'erroring' | 'errored';\n\ninterface WriteOrCloseRequest {\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n}\n\ntype WriteRequest = WriteOrCloseRequest;\ntype CloseRequest = WriteOrCloseRequest;\n\ninterface PendingAbortRequest {\n  _promise: Promise<undefined>;\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n  _reason: any;\n  _wasAlreadyErroring: boolean;\n}\n\n/**\n * A writable stream represents a destination for data, into which you can write.\n *\n * @public\n */\nclass WritableStream<W = any> {\n  /** @internal */\n  _state!: WritableStreamState;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _writer: WritableStreamDefaultWriter<W> | undefined;\n  /** @internal */\n  _writableStreamController!: WritableStreamDefaultController<W>;\n  /** @internal */\n  _writeRequests!: SimpleQueue<WriteRequest>;\n  /** @internal */\n  _inFlightWriteRequest: WriteRequest | undefined;\n  /** @internal */\n  _closeRequest: CloseRequest | undefined;\n  /** @internal */\n  _inFlightCloseRequest: CloseRequest | undefined;\n  /** @internal */\n  _pendingAbortRequest: PendingAbortRequest | undefined;\n  /** @internal */\n  _backpressure!: boolean;\n\n  constructor(underlyingSink?: UnderlyingSink<W>, strategy?: QueuingStrategy<W>);\n  constructor(rawUnderlyingSink: UnderlyingSink<W> | null | undefined = {},\n              rawStrategy: QueuingStrategy<W> | null | undefined = {}) {\n    if (rawUnderlyingSink === undefined) {\n      rawUnderlyingSink = null;\n    } else {\n      assertObject(rawUnderlyingSink, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSink = convertUnderlyingSink(rawUnderlyingSink, 'First parameter');\n\n    InitializeWritableStream(this);\n\n    const type = underlyingSink.type;\n    if (type !== undefined) {\n      throw new RangeError('Invalid type is specified');\n    }\n\n    const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n    const highWaterMark = ExtractHighWaterMark(strategy, 1);\n\n    SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);\n  }\n\n  /**\n   * Returns whether or not the writable stream is locked to a writer.\n   */\n  get locked(): boolean {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsWritableStreamLocked(this);\n  }\n\n  /**\n   * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be\n   * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort\n   * mechanism of the underlying sink.\n   *\n   * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled\n   * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel\n   * the stream) if the stream is currently locked.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('abort'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot abort a stream that already has a writer'));\n    }\n\n    return WritableStreamAbort(this, reason);\n  }\n\n  /**\n   * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its\n   * close behavior. During this time any further attempts to write will fail (without erroring the stream).\n   *\n   * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream\n   * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with\n   * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.\n   */\n  close() {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('close'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close a stream that already has a writer'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamClose(this);\n  }\n\n  /**\n   * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream\n   * is locked, no other writer can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to write to a stream\n   * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at\n   * the same time, which would cause the resulting written data to be unpredictable and probably useless.\n   */\n  getWriter(): WritableStreamDefaultWriter<W> {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('getWriter');\n    }\n\n    return AcquireWritableStreamDefaultWriter(this);\n  }\n}\n\nObject.defineProperties(WritableStream.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  getWriter: { enumerable: true },\n  locked: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStream.prototype, Symbol.toStringTag, {\n    value: 'WritableStream',\n    configurable: true\n  });\n}\n\nexport {\n  AcquireWritableStreamDefaultWriter,\n  CreateWritableStream,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamDefaultControllerErrorIfNeeded,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite,\n  WritableStreamCloseQueuedOrInFlight,\n  UnderlyingSink,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkAbortCallback\n};\n\n// Abstract operations for the WritableStream.\n\nfunction AcquireWritableStreamDefaultWriter<W>(stream: WritableStream<W>): WritableStreamDefaultWriter<W> {\n  return new WritableStreamDefaultWriter(stream);\n}\n\n// Throws if and only if startAlgorithm throws.\nfunction CreateWritableStream<W>(startAlgorithm: () => void | PromiseLike<void>,\n                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                 closeAlgorithm: () => Promise<void>,\n                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                 highWaterMark = 1,\n                                 sizeAlgorithm: QueuingStrategySizeCallback<W> = () => 1) {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: WritableStream<W> = Object.create(WritableStream.prototype);\n  InitializeWritableStream(stream);\n\n  const controller: WritableStreamDefaultController<W> = Object.create(WritableStreamDefaultController.prototype);\n\n  SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm,\n                                       abortAlgorithm, highWaterMark, sizeAlgorithm);\n  return stream;\n}\n\nfunction InitializeWritableStream<W>(stream: WritableStream<W>) {\n  stream._state = 'writable';\n\n  // The error that will be reported by new method calls once the state becomes errored. Only set when [[state]] is\n  // 'erroring' or 'errored'. May be set to an undefined value.\n  stream._storedError = undefined;\n\n  stream._writer = undefined;\n\n  // Initialize to undefined first because the constructor of the controller checks this\n  // variable to validate the caller.\n  stream._writableStreamController = undefined!;\n\n  // This queue is placed here instead of the writer class in order to allow for passing a writer to the next data\n  // producer without waiting for the queued writes to finish.\n  stream._writeRequests = new SimpleQueue();\n\n  // Write requests are removed from _writeRequests when write() is called on the underlying sink. This prevents\n  // them from being erroneously rejected on error. If a write() call is in-flight, the request is stored here.\n  stream._inFlightWriteRequest = undefined;\n\n  // The promise that was returned from writer.close(). Stored here because it may be fulfilled after the writer\n  // has been detached.\n  stream._closeRequest = undefined;\n\n  // Close request is removed from _closeRequest when close() is called on the underlying sink. This prevents it\n  // from being erroneously rejected on error. If a close() call is in-flight, the request is stored here.\n  stream._inFlightCloseRequest = undefined;\n\n  // The promise that was returned from writer.abort(). This may also be fulfilled after the writer has detached.\n  stream._pendingAbortRequest = undefined;\n\n  // The backpressure signal set by the controller.\n  stream._backpressure = false;\n}\n\nfunction IsWritableStream(x: unknown): x is WritableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_writableStreamController')) {\n    return false;\n  }\n\n  return x instanceof WritableStream;\n}\n\nfunction IsWritableStreamLocked(stream: WritableStream): boolean {\n  assert(IsWritableStream(stream));\n\n  if (stream._writer === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamAbort(stream: WritableStream, reason: any): Promise<undefined> {\n  if (stream._state === 'closed' || stream._state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  stream._writableStreamController._abortReason = reason;\n  stream._writableStreamController._abortController?.abort();\n\n  // TypeScript narrows the type of `stream._state` down to 'writable' | 'erroring',\n  // but it doesn't know that signaling abort runs author code that might have changed the state.\n  // Widen the type again by casting to WritableStreamState.\n  const state = stream._state as WritableStreamState;\n\n  if (state === 'closed' || state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._pendingAbortRequest !== undefined) {\n    return stream._pendingAbortRequest._promise;\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  let wasAlreadyErroring = false;\n  if (state === 'erroring') {\n    wasAlreadyErroring = true;\n    // reason will not be used, so don't keep a reference to it.\n    reason = undefined;\n  }\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    stream._pendingAbortRequest = {\n      _promise: undefined!,\n      _resolve: resolve,\n      _reject: reject,\n      _reason: reason,\n      _wasAlreadyErroring: wasAlreadyErroring\n    };\n  });\n  stream._pendingAbortRequest!._promise = promise;\n\n  if (!wasAlreadyErroring) {\n    WritableStreamStartErroring(stream, reason);\n  }\n\n  return promise;\n}\n\nfunction WritableStreamClose(stream: WritableStream<any>): Promise<undefined> {\n  const state = stream._state;\n  if (state === 'closed' || state === 'errored') {\n    return promiseRejectedWith(new TypeError(\n      `The stream (in ${state} state) is not in the writable state and cannot be closed`));\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const closeRequest: CloseRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._closeRequest = closeRequest;\n  });\n\n  const writer = stream._writer;\n  if (writer !== undefined && stream._backpressure && state === 'writable') {\n    defaultWriterReadyPromiseResolve(writer);\n  }\n\n  WritableStreamDefaultControllerClose(stream._writableStreamController);\n\n  return promise;\n}\n\n// WritableStream API exposed for controllers.\n\nfunction WritableStreamAddWriteRequest(stream: WritableStream): Promise<undefined> {\n  assert(IsWritableStreamLocked(stream));\n  assert(stream._state === 'writable');\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const writeRequest: WriteRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._writeRequests.push(writeRequest);\n  });\n\n  return promise;\n}\n\nfunction WritableStreamDealWithRejection(stream: WritableStream, error: any) {\n  const state = stream._state;\n\n  if (state === 'writable') {\n    WritableStreamStartErroring(stream, error);\n    return;\n  }\n\n  assert(state === 'erroring');\n  WritableStreamFinishErroring(stream);\n}\n\nfunction WritableStreamStartErroring(stream: WritableStream, reason: any) {\n  assert(stream._storedError === undefined);\n  assert(stream._state === 'writable');\n\n  const controller = stream._writableStreamController;\n  assert(controller !== undefined);\n\n  stream._state = 'erroring';\n  stream._storedError = reason;\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason);\n  }\n\n  if (!WritableStreamHasOperationMarkedInFlight(stream) && controller._started) {\n    WritableStreamFinishErroring(stream);\n  }\n}\n\nfunction WritableStreamFinishErroring(stream: WritableStream) {\n  assert(stream._state === 'erroring');\n  assert(!WritableStreamHasOperationMarkedInFlight(stream));\n  stream._state = 'errored';\n  stream._writableStreamController[ErrorSteps]();\n\n  const storedError = stream._storedError;\n  stream._writeRequests.forEach(writeRequest => {\n    writeRequest._reject(storedError);\n  });\n  stream._writeRequests = new SimpleQueue();\n\n  if (stream._pendingAbortRequest === undefined) {\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const abortRequest = stream._pendingAbortRequest;\n  stream._pendingAbortRequest = undefined;\n\n  if (abortRequest._wasAlreadyErroring) {\n    abortRequest._reject(storedError);\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const promise = stream._writableStreamController[AbortSteps](abortRequest._reason);\n  uponPromise(\n    promise,\n    () => {\n      abortRequest._resolve();\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    },\n    (reason: any) => {\n      abortRequest._reject(reason);\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    });\n}\n\nfunction WritableStreamFinishInFlightWrite(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._resolve(undefined);\n  stream._inFlightWriteRequest = undefined;\n}\n\nfunction WritableStreamFinishInFlightWriteWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._reject(error);\n  stream._inFlightWriteRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  WritableStreamDealWithRejection(stream, error);\n}\n\nfunction WritableStreamFinishInFlightClose(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._resolve(undefined);\n  stream._inFlightCloseRequest = undefined;\n\n  const state = stream._state;\n\n  assert(state === 'writable' || state === 'erroring');\n\n  if (state === 'erroring') {\n    // The error was too late to do anything, so it is ignored.\n    stream._storedError = undefined;\n    if (stream._pendingAbortRequest !== undefined) {\n      stream._pendingAbortRequest._resolve();\n      stream._pendingAbortRequest = undefined;\n    }\n  }\n\n  stream._state = 'closed';\n\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseResolve(writer);\n  }\n\n  assert(stream._pendingAbortRequest === undefined);\n  assert(stream._storedError === undefined);\n}\n\nfunction WritableStreamFinishInFlightCloseWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._reject(error);\n  stream._inFlightCloseRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  // Never execute sink abort() after sink close().\n  if (stream._pendingAbortRequest !== undefined) {\n    stream._pendingAbortRequest._reject(error);\n    stream._pendingAbortRequest = undefined;\n  }\n  WritableStreamDealWithRejection(stream, error);\n}\n\n// TODO(ricea): Fix alphabetical order.\nfunction WritableStreamCloseQueuedOrInFlight(stream: WritableStream): boolean {\n  if (stream._closeRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamHasOperationMarkedInFlight(stream: WritableStream): boolean {\n  if (stream._inFlightWriteRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamMarkCloseRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest === undefined);\n  assert(stream._closeRequest !== undefined);\n  stream._inFlightCloseRequest = stream._closeRequest;\n  stream._closeRequest = undefined;\n}\n\nfunction WritableStreamMarkFirstWriteRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest === undefined);\n  assert(stream._writeRequests.length !== 0);\n  stream._inFlightWriteRequest = stream._writeRequests.shift();\n}\n\nfunction WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream: WritableStream) {\n  assert(stream._state === 'errored');\n  if (stream._closeRequest !== undefined) {\n    assert(stream._inFlightCloseRequest === undefined);\n\n    stream._closeRequest._reject(stream._storedError);\n    stream._closeRequest = undefined;\n  }\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseReject(writer, stream._storedError);\n  }\n}\n\nfunction WritableStreamUpdateBackpressure(stream: WritableStream, backpressure: boolean) {\n  assert(stream._state === 'writable');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const writer = stream._writer;\n  if (writer !== undefined && backpressure !== stream._backpressure) {\n    if (backpressure) {\n      defaultWriterReadyPromiseReset(writer);\n    } else {\n      assert(!backpressure);\n\n      defaultWriterReadyPromiseResolve(writer);\n    }\n  }\n\n  stream._backpressure = backpressure;\n}\n\n/**\n * A default writer vended by a {@link WritableStream}.\n *\n * @public\n */\nexport class WritableStreamDefaultWriter<W = any> {\n  /** @internal */\n  _ownerWritableStream: WritableStream<W>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _closedPromiseState!: 'pending' | 'resolved' | 'rejected';\n  /** @internal */\n  _readyPromise!: Promise<undefined>;\n  /** @internal */\n  _readyPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _readyPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readyPromiseState!: 'pending' | 'fulfilled' | 'rejected';\n\n  constructor(stream: WritableStream<W>) {\n    assertRequiredArgument(stream, 1, 'WritableStreamDefaultWriter');\n    assertWritableStream(stream, 'First parameter');\n\n    if (IsWritableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive writing by another writer');\n    }\n\n    this._ownerWritableStream = stream;\n    stream._writer = this;\n\n    const state = stream._state;\n\n    if (state === 'writable') {\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure) {\n        defaultWriterReadyPromiseInitialize(this);\n      } else {\n        defaultWriterReadyPromiseInitializeAsResolved(this);\n      }\n\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'erroring') {\n      defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError);\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'closed') {\n      defaultWriterReadyPromiseInitializeAsResolved(this);\n      defaultWriterClosedPromiseInitializeAsResolved(this);\n    } else {\n      assert(state === 'errored');\n\n      const storedError = stream._storedError;\n      defaultWriterReadyPromiseInitializeAsRejected(this, storedError);\n      defaultWriterClosedPromiseInitializeAsRejected(this, storedError);\n    }\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the writer’s lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.\n   * A producer can use this information to determine the right amount of data to write.\n   *\n   * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort\n   * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when\n   * the writer’s lock is released.\n   */\n  get desiredSize(): number | null {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('desiredSize');\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      throw defaultWriterLockException('desiredSize');\n    }\n\n    return WritableStreamDefaultWriterGetDesiredSize(this);\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions\n   * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips\n   * back to zero or below, the getter will return a new promise that stays pending until the next transition.\n   *\n   * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become\n   * rejected.\n   */\n  get ready(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('ready'));\n    }\n\n    return this._readyPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('abort'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('abort'));\n    }\n\n    return WritableStreamDefaultWriterAbort(this, reason);\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.\n   */\n  close(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('close'));\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('close'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(stream)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamDefaultWriterClose(this);\n  }\n\n  /**\n   * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.\n   * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from\n   * now on; otherwise, the writer will appear closed.\n   *\n   * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the\n   * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).\n   * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents\n   * other producers from writing in an interleaved manner.\n   */\n  releaseLock(): void {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('releaseLock');\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return;\n    }\n\n    assert(stream._writer !== undefined);\n\n    WritableStreamDefaultWriterRelease(this);\n  }\n\n  /**\n   * Writes the given chunk to the writable stream, by waiting until any previous writes have finished successfully,\n   * and then sending the chunk to the underlying sink's {@link UnderlyingSink.write | write()} method. It will return\n   * a promise that fulfills with undefined upon a successful write, or rejects if the write fails or stream becomes\n   * errored before the writing process is initiated.\n   *\n   * Note that what \"success\" means is up to the underlying sink; it might indicate simply that the chunk has been\n   * accepted, and not necessarily that it is safely saved to its ultimate destination.\n   */\n  write(chunk: W): Promise<void>;\n  write(chunk: W = undefined!): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('write'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('write to'));\n    }\n\n    return WritableStreamDefaultWriterWrite(this, chunk);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  releaseLock: { enumerable: true },\n  write: { enumerable: true },\n  closed: { enumerable: true },\n  desiredSize: { enumerable: true },\n  ready: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultWriter.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultWriter',\n    configurable: true\n  });\n}\n\n// Abstract operations for the WritableStreamDefaultWriter.\n\nfunction IsWritableStreamDefaultWriter<W = any>(x: any): x is WritableStreamDefaultWriter<W> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_ownerWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultWriter;\n}\n\n// A client of WritableStreamDefaultWriter may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultWriterAbort(writer: WritableStreamDefaultWriter, reason: any) {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamAbort(stream, reason);\n}\n\nfunction WritableStreamDefaultWriterClose(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamClose(stream);\n}\n\nfunction WritableStreamDefaultWriterCloseWithErrorPropagation(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const state = stream._state;\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  return WritableStreamDefaultWriterClose(writer);\n}\n\nfunction WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._closedPromiseState === 'pending') {\n    defaultWriterClosedPromiseReject(writer, error);\n  } else {\n    defaultWriterClosedPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._readyPromiseState === 'pending') {\n    defaultWriterReadyPromiseReject(writer, error);\n  } else {\n    defaultWriterReadyPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterGetDesiredSize(writer: WritableStreamDefaultWriter): number | null {\n  const stream = writer._ownerWritableStream;\n  const state = stream._state;\n\n  if (state === 'errored' || state === 'erroring') {\n    return null;\n  }\n\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);\n}\n\nfunction WritableStreamDefaultWriterRelease(writer: WritableStreamDefaultWriter) {\n  const stream = writer._ownerWritableStream;\n  assert(stream !== undefined);\n  assert(stream._writer === writer);\n\n  const releasedError = new TypeError(\n    `Writer was released and can no longer be used to monitor the stream's closedness`);\n\n  WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError);\n\n  // The state transitions to \"errored\" before the sink abort() method runs, but the writer.closed promise is not\n  // rejected until afterwards. This means that simply testing state will not work.\n  WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError);\n\n  stream._writer = undefined;\n  writer._ownerWritableStream = undefined!;\n}\n\nfunction WritableStreamDefaultWriterWrite<W>(writer: WritableStreamDefaultWriter<W>, chunk: W): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const controller = stream._writableStreamController;\n\n  const chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);\n\n  if (stream !== writer._ownerWritableStream) {\n    return promiseRejectedWith(defaultWriterLockException('write to'));\n  }\n\n  const state = stream._state;\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseRejectedWith(new TypeError('The stream is closing or closed and cannot be written to'));\n  }\n  if (state === 'erroring') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable');\n\n  const promise = WritableStreamAddWriteRequest(stream);\n\n  WritableStreamDefaultControllerWrite(controller, chunk, chunkSize);\n\n  return promise;\n}\n\nconst closeSentinel: unique symbol = {} as any;\n\ntype QueueRecord<W> = W | typeof closeSentinel;\n\n/**\n * Allows control of a {@link WritableStream | writable stream}'s state and internal queue.\n *\n * @public\n */\nexport class WritableStreamDefaultController<W = any> {\n  /** @internal */\n  _controlledWritableStream!: WritableStream<W>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<QueueRecord<W>>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _abortReason: any;\n  /** @internal */\n  _abortController: AbortController | undefined;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<W>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _writeAlgorithm!: (chunk: W) => Promise<void>;\n  /** @internal */\n  _closeAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _abortAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * The reason which was passed to `WritableStream.abort(reason)` when the stream was aborted.\n   *\n   * @deprecated\n   *  This property has been removed from the specification, see https://github.com/whatwg/streams/pull/1177.\n   *  Use {@link WritableStreamDefaultController.signal}'s `reason` instead.\n   */\n  get abortReason(): any {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('abortReason');\n    }\n    return this._abortReason;\n  }\n\n  /**\n   * An `AbortSignal` that can be used to abort the pending write or close operation when the stream is aborted.\n   */\n  get signal(): AbortSignal {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('signal');\n    }\n    if (this._abortController === undefined) {\n      // Older browsers or older Node versions may not support `AbortController` or `AbortSignal`.\n      // We don't want to bundle and ship an `AbortController` polyfill together with our polyfill,\n      // so instead we only implement support for `signal` if we find a global `AbortController` constructor.\n      throw new TypeError('WritableStreamDefaultController.prototype.signal is not supported');\n    }\n    return this._abortController.signal;\n  }\n\n  /**\n   * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.\n   *\n   * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying\n   * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the\n   * normal lifecycle of interactions with the underlying sink.\n   */\n  error(e: any = undefined): void {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n    const state = this._controlledWritableStream._state;\n    if (state !== 'writable') {\n      // The stream is closed, errored or will be soon. The sink can't do anything useful if it gets an error here, so\n      // just treat it as a no-op.\n      return;\n    }\n\n    WritableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [AbortSteps](reason: any): Promise<void> {\n    const result = this._abortAlgorithm(reason);\n    WritableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [ErrorSteps]() {\n    ResetQueue(this);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n  abortReason: { enumerable: true },\n  signal: { enumerable: true },\n  error: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations implementing interface required by the WritableStream.\n\nfunction IsWritableStreamDefaultController(x: any): x is WritableStreamDefaultController<any> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultController;\n}\n\nfunction SetUpWritableStreamDefaultController<W>(stream: WritableStream<W>,\n                                                 controller: WritableStreamDefaultController<W>,\n                                                 startAlgorithm: () => void | PromiseLike<void>,\n                                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                                 closeAlgorithm: () => Promise<void>,\n                                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                                 highWaterMark: number,\n                                                 sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  assert(IsWritableStream(stream));\n  assert(stream._writableStreamController === undefined);\n\n  controller._controlledWritableStream = stream;\n  stream._writableStreamController = controller;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._abortReason = undefined;\n  controller._abortController = createAbortController();\n  controller._started = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._writeAlgorithm = writeAlgorithm;\n  controller._closeAlgorithm = closeAlgorithm;\n  controller._abortAlgorithm = abortAlgorithm;\n\n  const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n  WritableStreamUpdateBackpressure(stream, backpressure);\n\n  const startResult = startAlgorithm();\n  const startPromise = promiseResolvedWith(startResult);\n  uponPromise(\n    startPromise,\n    () => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n    },\n    r => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDealWithRejection(stream, r);\n    }\n  );\n}\n\nfunction SetUpWritableStreamDefaultControllerFromUnderlyingSink<W>(stream: WritableStream<W>,\n                                                                   underlyingSink: ValidatedUnderlyingSink<W>,\n                                                                   highWaterMark: number,\n                                                                   sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  const controller = Object.create(WritableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let writeAlgorithm: (chunk: W) => Promise<void> = () => promiseResolvedWith(undefined);\n  let closeAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let abortAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingSink.start !== undefined) {\n    startAlgorithm = () => underlyingSink.start!(controller);\n  }\n  if (underlyingSink.write !== undefined) {\n    writeAlgorithm = chunk => underlyingSink.write!(chunk, controller);\n  }\n  if (underlyingSink.close !== undefined) {\n    closeAlgorithm = () => underlyingSink.close!();\n  }\n  if (underlyingSink.abort !== undefined) {\n    abortAlgorithm = reason => underlyingSink.abort!(reason);\n  }\n\n  SetUpWritableStreamDefaultController(\n    stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// ClearAlgorithms may be called twice. Erroring the same stream in multiple ways will often result in redundant calls.\nfunction WritableStreamDefaultControllerClearAlgorithms(controller: WritableStreamDefaultController<any>) {\n  controller._writeAlgorithm = undefined!;\n  controller._closeAlgorithm = undefined!;\n  controller._abortAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\nfunction WritableStreamDefaultControllerClose<W>(controller: WritableStreamDefaultController<W>) {\n  EnqueueValueWithSize(controller, closeSentinel, 0);\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\nfunction WritableStreamDefaultControllerGetChunkSize<W>(controller: WritableStreamDefaultController<W>,\n                                                        chunk: W): number {\n  try {\n    return controller._strategySizeAlgorithm(chunk);\n  } catch (chunkSizeE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE);\n    return 1;\n  }\n}\n\nfunction WritableStreamDefaultControllerGetDesiredSize(controller: WritableStreamDefaultController<any>): number {\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nfunction WritableStreamDefaultControllerWrite<W>(controller: WritableStreamDefaultController<W>,\n                                                 chunk: W,\n                                                 chunkSize: number) {\n  try {\n    EnqueueValueWithSize(controller, chunk, chunkSize);\n  } catch (enqueueE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);\n    return;\n  }\n\n  const stream = controller._controlledWritableStream;\n  if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === 'writable') {\n    const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n    WritableStreamUpdateBackpressure(stream, backpressure);\n  }\n\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\n// Abstract operations for the WritableStreamDefaultController.\n\nfunction WritableStreamDefaultControllerAdvanceQueueIfNeeded<W>(controller: WritableStreamDefaultController<W>) {\n  const stream = controller._controlledWritableStream;\n\n  if (!controller._started) {\n    return;\n  }\n\n  if (stream._inFlightWriteRequest !== undefined) {\n    return;\n  }\n\n  const state = stream._state;\n  assert(state !== 'closed' && state !== 'errored');\n  if (state === 'erroring') {\n    WritableStreamFinishErroring(stream);\n    return;\n  }\n\n  if (controller._queue.length === 0) {\n    return;\n  }\n\n  const value = PeekQueueValue(controller);\n  if (value === closeSentinel) {\n    WritableStreamDefaultControllerProcessClose(controller);\n  } else {\n    WritableStreamDefaultControllerProcessWrite(controller, value);\n  }\n}\n\nfunction WritableStreamDefaultControllerErrorIfNeeded(controller: WritableStreamDefaultController<any>, error: any) {\n  if (controller._controlledWritableStream._state === 'writable') {\n    WritableStreamDefaultControllerError(controller, error);\n  }\n}\n\nfunction WritableStreamDefaultControllerProcessClose(controller: WritableStreamDefaultController<any>) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkCloseRequestInFlight(stream);\n\n  DequeueValue(controller);\n  assert(controller._queue.length === 0);\n\n  const sinkClosePromise = controller._closeAlgorithm();\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  uponPromise(\n    sinkClosePromise,\n    () => {\n      WritableStreamFinishInFlightClose(stream);\n    },\n    reason => {\n      WritableStreamFinishInFlightCloseWithError(stream, reason);\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerProcessWrite<W>(controller: WritableStreamDefaultController<W>, chunk: W) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkFirstWriteRequestInFlight(stream);\n\n  const sinkWritePromise = controller._writeAlgorithm(chunk);\n  uponPromise(\n    sinkWritePromise,\n    () => {\n      WritableStreamFinishInFlightWrite(stream);\n\n      const state = stream._state;\n      assert(state === 'writable' || state === 'erroring');\n\n      DequeueValue(controller);\n\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && state === 'writable') {\n        const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n        WritableStreamUpdateBackpressure(stream, backpressure);\n      }\n\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n    },\n    reason => {\n      if (stream._state === 'writable') {\n        WritableStreamDefaultControllerClearAlgorithms(controller);\n      }\n      WritableStreamFinishInFlightWriteWithError(stream, reason);\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerGetBackpressure(controller: WritableStreamDefaultController<any>): boolean {\n  const desiredSize = WritableStreamDefaultControllerGetDesiredSize(controller);\n  return desiredSize <= 0;\n}\n\n// A client of WritableStreamDefaultController may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultControllerError(controller: WritableStreamDefaultController<any>, error: any) {\n  const stream = controller._controlledWritableStream;\n\n  assert(stream._state === 'writable');\n\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  WritableStreamStartErroring(stream, error);\n}\n\n// Helper functions for the WritableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`WritableStream.prototype.${name} can only be used on a WritableStream`);\n}\n\n// Helper functions for the WritableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultController.prototype.${name} can only be used on a WritableStreamDefaultController`);\n}\n\n\n// Helper functions for the WritableStreamDefaultWriter.\n\nfunction defaultWriterBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultWriter.prototype.${name} can only be used on a WritableStreamDefaultWriter`);\n}\n\nfunction defaultWriterLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released writer');\n}\n\nfunction defaultWriterClosedPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._closedPromise = newPromise((resolve, reject) => {\n    writer._closedPromise_resolve = resolve;\n    writer._closedPromise_reject = reject;\n    writer._closedPromiseState = 'pending';\n  });\n}\n\nfunction defaultWriterClosedPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseReject(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseResolve(writer);\n}\n\nfunction defaultWriterClosedPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._closedPromise_reject === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  setPromiseIsHandledToTrue(writer._closedPromise);\n  writer._closedPromise_reject(reason);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'rejected';\n}\n\nfunction defaultWriterClosedPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._closedPromise_resolve === undefined);\n  assert(writer._closedPromise_reject === undefined);\n  assert(writer._closedPromiseState !== 'pending');\n\n  defaultWriterClosedPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._closedPromise_resolve === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  writer._closedPromise_resolve(undefined);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'resolved';\n}\n\nfunction defaultWriterReadyPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._readyPromise = newPromise((resolve, reject) => {\n    writer._readyPromise_resolve = resolve;\n    writer._readyPromise_reject = reject;\n  });\n  writer._readyPromiseState = 'pending';\n}\n\nfunction defaultWriterReadyPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseReject(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseResolve(writer);\n}\n\nfunction defaultWriterReadyPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._readyPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(writer._readyPromise);\n  writer._readyPromise_reject(reason);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'rejected';\n}\n\nfunction defaultWriterReadyPromiseReset(writer: WritableStreamDefaultWriter) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitialize(writer);\n}\n\nfunction defaultWriterReadyPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._readyPromise_resolve === undefined) {\n    return;\n  }\n\n  writer._readyPromise_resolve(undefined);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'fulfilled';\n}\n", "/// <reference lib=\"dom\" />\nexport const NativeDOMException: typeof DOMException | undefined =\n  typeof DOMException !== 'undefined' ? DOMException : undefined;\n", "/// <reference types=\"node\" />\nimport { NativeDOMException } from './native';\n\ndeclare class DOMExceptionClass extends Error {\n  constructor(message?: string, name?: string);\n\n  name: string;\n  message: string;\n}\n\ntype DOMException = DOMExceptionClass;\ntype DOMExceptionConstructor = typeof DOMExceptionClass;\n\nfunction isDOMExceptionConstructor(ctor: unknown): ctor is DOMExceptionConstructor {\n  if (!(typeof ctor === 'function' || typeof ctor === 'object')) {\n    return false;\n  }\n  try {\n    new (ctor as DOMExceptionConstructor)();\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction createDOMExceptionPolyfill(): DOMExceptionConstructor {\n  // eslint-disable-next-line no-shadow\n  const ctor = function DOMException(this: DOMException, message?: string, name?: string) {\n    this.message = message || '';\n    this.name = name || 'Error';\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  } as any;\n  ctor.prototype = Object.create(Error.prototype);\n  Object.defineProperty(ctor.prototype, 'constructor', { value: ctor, writable: true, configurable: true });\n  return ctor;\n}\n\n// eslint-disable-next-line no-redeclare\nconst DOMException: DOMExceptionConstructor =\n  isDOMExceptionConstructor(NativeDOMException) ? NativeDOMException : createDOMExceptionPolyfill();\n\nexport { DOMException };\n", "import { IsReadableStream, IsReadableStreamLocked, ReadableStream, ReadableStreamCancel } from '../readable-stream';\nimport { AcquireReadableStreamDefaultReader, ReadableStreamDefaultReaderRead } from './default-reader';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireWritableStreamDefaultWriter,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamCloseQueuedOrInFlight,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite\n} from '../writable-stream';\nimport assert from '../../stub/assert';\nimport {\n  newPromise,\n  PerformPromiseThen,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponFulfillment,\n  uponPromise,\n  uponRejection\n} from '../helpers/webidl';\nimport { noop } from '../../utils';\nimport { AbortSignal, isAbortSignal } from '../abort-signal';\nimport { DOMException } from '../../stub/dom-exception';\n\nexport function ReadableStreamPipeTo<T>(source: ReadableStream<T>,\n                                        dest: WritableStream<T>,\n                                        preventClose: boolean,\n                                        preventAbort: boolean,\n                                        preventCancel: boolean,\n                                        signal: AbortSignal | undefined): Promise<undefined> {\n  assert(IsReadableStream(source));\n  assert(IsWritableStream(dest));\n  assert(typeof preventClose === 'boolean');\n  assert(typeof preventAbort === 'boolean');\n  assert(typeof preventCancel === 'boolean');\n  assert(signal === undefined || isAbortSignal(signal));\n  assert(!IsReadableStreamLocked(source));\n  assert(!IsWritableStreamLocked(dest));\n\n  const reader = AcquireReadableStreamDefaultReader<T>(source);\n  const writer = AcquireWritableStreamDefaultWriter<T>(dest);\n\n  source._disturbed = true;\n\n  let shuttingDown = false;\n\n  // This is used to keep track of the spec's requirement that we wait for ongoing writes during shutdown.\n  let currentWrite = promiseResolvedWith<void>(undefined);\n\n  return newPromise((resolve, reject) => {\n    let abortAlgorithm: () => void;\n    if (signal !== undefined) {\n      abortAlgorithm = () => {\n        const error = new DOMException('Aborted', 'AbortError');\n        const actions: Array<() => Promise<void>> = [];\n        if (!preventAbort) {\n          actions.push(() => {\n            if (dest._state === 'writable') {\n              return WritableStreamAbort(dest, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        if (!preventCancel) {\n          actions.push(() => {\n            if (source._state === 'readable') {\n              return ReadableStreamCancel(source, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        shutdownWithAction(() => Promise.all(actions.map(action => action())), true, error);\n      };\n\n      if (signal.aborted) {\n        abortAlgorithm();\n        return;\n      }\n\n      signal.addEventListener('abort', abortAlgorithm);\n    }\n\n    // Using reader and writer, read all chunks from this and write them to dest\n    // - Backpressure must be enforced\n    // - Shutdown must stop all activity\n    function pipeLoop() {\n      return newPromise<void>((resolveLoop, rejectLoop) => {\n        function next(done: boolean) {\n          if (done) {\n            resolveLoop();\n          } else {\n            // Use `PerformPromiseThen` instead of `uponPromise` to avoid\n            // adding unnecessary `.catch(rethrowAssertionErrorRejection)` handlers\n            PerformPromiseThen(pipeStep(), next, rejectLoop);\n          }\n        }\n\n        next(false);\n      });\n    }\n\n    function pipeStep(): Promise<boolean> {\n      if (shuttingDown) {\n        return promiseResolvedWith(true);\n      }\n\n      return PerformPromiseThen(writer._readyPromise, () => {\n        return newPromise<boolean>((resolveRead, rejectRead) => {\n          ReadableStreamDefaultReaderRead(\n            reader,\n            {\n              _chunkSteps: chunk => {\n                currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), undefined, noop);\n                resolveRead(false);\n              },\n              _closeSteps: () => resolveRead(true),\n              _errorSteps: rejectRead\n            }\n          );\n        });\n      });\n    }\n\n    // Errors must be propagated forward\n    isOrBecomesErrored(source, reader._closedPromise, storedError => {\n      if (!preventAbort) {\n        shutdownWithAction(() => WritableStreamAbort(dest, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n    });\n\n    // Errors must be propagated backward\n    isOrBecomesErrored(dest, writer._closedPromise, storedError => {\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n    });\n\n    // Closing must be propagated forward\n    isOrBecomesClosed(source, reader._closedPromise, () => {\n      if (!preventClose) {\n        shutdownWithAction(() => WritableStreamDefaultWriterCloseWithErrorPropagation(writer));\n      } else {\n        shutdown();\n      }\n    });\n\n    // Closing must be propagated backward\n    if (WritableStreamCloseQueuedOrInFlight(dest) || dest._state === 'closed') {\n      const destClosed = new TypeError('the destination writable stream closed before all data could be piped to it');\n\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, destClosed), true, destClosed);\n      } else {\n        shutdown(true, destClosed);\n      }\n    }\n\n    setPromiseIsHandledToTrue(pipeLoop());\n\n    function waitForWritesToFinish(): Promise<void> {\n      // Another write may have started while we were waiting on this currentWrite, so we have to be sure to wait\n      // for that too.\n      const oldCurrentWrite = currentWrite;\n      return PerformPromiseThen(\n        currentWrite,\n        () => oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : undefined\n      );\n    }\n\n    function isOrBecomesErrored(stream: ReadableStream | WritableStream,\n                                promise: Promise<void>,\n                                action: (reason: any) => void) {\n      if (stream._state === 'errored') {\n        action(stream._storedError);\n      } else {\n        uponRejection(promise, action);\n      }\n    }\n\n    function isOrBecomesClosed(stream: ReadableStream | WritableStream, promise: Promise<void>, action: () => void) {\n      if (stream._state === 'closed') {\n        action();\n      } else {\n        uponFulfillment(promise, action);\n      }\n    }\n\n    function shutdownWithAction(action: () => Promise<unknown>, originalIsError?: boolean, originalError?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), doTheRest);\n      } else {\n        doTheRest();\n      }\n\n      function doTheRest() {\n        uponPromise(\n          action(),\n          () => finalize(originalIsError, originalError),\n          newError => finalize(true, newError)\n        );\n      }\n    }\n\n    function shutdown(isError?: boolean, error?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), () => finalize(isError, error));\n      } else {\n        finalize(isError, error);\n      }\n    }\n\n    function finalize(isError?: boolean, error?: any) {\n      WritableStreamDefaultWriterRelease(writer);\n      ReadableStreamReaderGenericRelease(reader);\n\n      if (signal !== undefined) {\n        signal.removeEventListener('abort', abortAlgorithm);\n      }\n      if (isError) {\n        reject(error);\n      } else {\n        resolve(undefined);\n      }\n    }\n  });\n}\n", "import { QueuingStrategySizeCallback } from '../queuing-strategy';\nimport assert from '../../stub/assert';\nimport { DequeueValue, EnqueueValueWithSize, QueuePair, ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadRequest\n} from './default-reader';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsReadableStreamLocked, ReadableStream, ReadableStreamClose, ReadableStreamError } from '../readable-stream';\nimport { ValidatedUnderlyingSource } from './underlying-source';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { CancelSteps, PullSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\n\n/**\n * Allows control of a {@link ReadableStream | readable stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableStreamDefaultController<R> {\n  /** @internal */\n  _controlledReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<R>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<R>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableStreamDefaultControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('close');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits close');\n    }\n\n    ReadableStreamDefaultControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the controlled readable stream.\n   */\n  enqueue(chunk: R): void;\n  enqueue(chunk: R = undefined!): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits enqueue');\n    }\n\n    return ReadableStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    ReadableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ResetQueue(this);\n    const result = this._cancelAlgorithm(reason);\n    ReadableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<R>): void {\n    const stream = this._controlledReadableStream;\n\n    if (this._queue.length > 0) {\n      const chunk = DequeueValue(this);\n\n      if (this._closeRequested && this._queue.length === 0) {\n        ReadableStreamDefaultControllerClearAlgorithms(this);\n        ReadableStreamClose(stream);\n      } else {\n        ReadableStreamDefaultControllerCallPullIfNeeded(this);\n      }\n\n      readRequest._chunkSteps(chunk);\n    } else {\n      ReadableStreamAddReadRequest(stream, readRequest);\n      ReadableStreamDefaultControllerCallPullIfNeeded(this);\n    }\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableStreamDefaultController.\n\nfunction IsReadableStreamDefaultController<R = any>(x: any): x is ReadableStreamDefaultController<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultController;\n}\n\nfunction ReadableStreamDefaultControllerCallPullIfNeeded(controller: ReadableStreamDefaultController<any>): void {\n  const shouldPull = ReadableStreamDefaultControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      }\n    },\n    e => {\n      ReadableStreamDefaultControllerError(controller, e);\n    }\n  );\n}\n\nfunction ReadableStreamDefaultControllerShouldCallPull(controller: ReadableStreamDefaultController<any>): boolean {\n  const stream = controller._controlledReadableStream;\n\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableStreamDefaultControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableStreamDefaultControllerClearAlgorithms(controller: ReadableStreamDefaultController<any>) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\n// A client of ReadableStreamDefaultController may use these functions directly to bypass state check.\n\nexport function ReadableStreamDefaultControllerClose(controller: ReadableStreamDefaultController<any>) {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  controller._closeRequested = true;\n\n  if (controller._queue.length === 0) {\n    ReadableStreamDefaultControllerClearAlgorithms(controller);\n    ReadableStreamClose(stream);\n  }\n}\n\nexport function ReadableStreamDefaultControllerEnqueue<R>(\n  controller: ReadableStreamDefaultController<R>,\n  chunk: R\n): void {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    ReadableStreamFulfillReadRequest(stream, chunk, false);\n  } else {\n    let chunkSize;\n    try {\n      chunkSize = controller._strategySizeAlgorithm(chunk);\n    } catch (chunkSizeE) {\n      ReadableStreamDefaultControllerError(controller, chunkSizeE);\n      throw chunkSizeE;\n    }\n\n    try {\n      EnqueueValueWithSize(controller, chunk, chunkSize);\n    } catch (enqueueE) {\n      ReadableStreamDefaultControllerError(controller, enqueueE);\n      throw enqueueE;\n    }\n  }\n\n  ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableStreamDefaultControllerError(controller: ReadableStreamDefaultController<any>, e: any) {\n  const stream = controller._controlledReadableStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ResetQueue(controller);\n\n  ReadableStreamDefaultControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableStreamDefaultControllerGetDesiredSize(\n  controller: ReadableStreamDefaultController<any>\n): number | null {\n  const state = controller._controlledReadableStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\n// This is used in the implementation of TransformStream.\nexport function ReadableStreamDefaultControllerHasBackpressure(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  if (ReadableStreamDefaultControllerShouldCallPull(controller)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function ReadableStreamDefaultControllerCanCloseOrEnqueue(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  const state = controller._controlledReadableStream._state;\n\n  if (!controller._closeRequested && state === 'readable') {\n    return true;\n  }\n\n  return false;\n}\n\nexport function SetUpReadableStreamDefaultController<R>(stream: ReadableStream<R>,\n                                                        controller: ReadableStreamDefaultController<R>,\n                                                        startAlgorithm: () => void | PromiseLike<void>,\n                                                        pullAlgorithm: () => Promise<void>,\n                                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                                        highWaterMark: number,\n                                                        sizeAlgorithm: QueuingStrategySizeCallback<R>) {\n  assert(stream._readableStreamController === undefined);\n\n  controller._controlledReadableStream = stream;\n\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._started = false;\n  controller._closeRequested = false;\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n    },\n    r => {\n      ReadableStreamDefaultControllerError(controller, r);\n    }\n  );\n}\n\nexport function SetUpReadableStreamDefaultControllerFromUnderlyingSource<R>(\n  stream: ReadableStream<R>,\n  underlyingSource: ValidatedUnderlyingSource<R>,\n  highWaterMark: number,\n  sizeAlgorithm: QueuingStrategySizeCallback<R>\n) {\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let pullAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let cancelAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingSource.start !== undefined) {\n    startAlgorithm = () => underlyingSource.start!(controller);\n  }\n  if (underlyingSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingSource.pull!(controller);\n  }\n  if (underlyingSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingSource.cancel!(reason);\n  }\n\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// Helper functions for the ReadableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultController.prototype.${name} can only be used on a ReadableStreamDefaultController`);\n}\n", "import {\n  CreateReadableByteStream,\n  CreateReadableStream,\n  IsReadableStream,\n  ReadableByteStream,\n  ReadableStream,\n  ReadableStreamCancel,\n  ReadableStreamReader\n} from '../readable-stream';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  ReadRequest\n} from './default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderRead,\n  ReadIntoRequest\n} from './byob-reader';\nimport assert from '../../stub/assert';\nimport { newPromise, promiseResolvedWith, queueMicrotask, uponRejection } from '../helpers/webidl';\nimport {\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError\n} from './default-controller';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamControllerClose,\n  ReadableByteStreamControllerEnqueue,\n  ReadableByteStreamControllerError,\n  ReadableByteStreamControllerGetBYOBRequest,\n  ReadableByteStreamControllerRespond,\n  ReadableByteStreamControllerRespondWithNewView\n} from './byte-stream-controller';\nimport { CreateArrayFromList } from '../abstract-ops/ecmascript';\nimport { CloneAsUint8Array } from '../abstract-ops/miscellaneous';\n\nexport function ReadableStreamTee<R>(stream: ReadableStream<R>,\n                                     cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n  if (IsReadableByteStreamController(stream._readableStreamController)) {\n    return ReadableByteStreamTee(stream as unknown as ReadableByteStream) as\n      unknown as [ReadableStream<R>, ReadableStream<R>];\n  }\n  return ReadableStreamDefaultTee(stream, cloneForBranch2);\n}\n\nexport function ReadableStreamDefaultTee<R>(stream: ReadableStream<R>,\n                                            cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n\n  let reading = false;\n  let readAgain = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableStream<R>;\n  let branch2: ReadableStream<R>;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<undefined>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function pullAlgorithm(): Promise<void> {\n    if (reading) {\n      readAgain = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgain = false;\n          const chunk1 = chunk;\n          const chunk2 = chunk;\n\n          // There is no way to access the cloning code right now in the reference implementation.\n          // If we add one then we'll need an implementation for serializable objects.\n          // if (!canceled2 && cloneForBranch2) {\n          //   chunk2 = StructuredDeserialize(StructuredSerialize(chunk2));\n          // }\n\n          if (!canceled1) {\n            ReadableStreamDefaultControllerEnqueue(\n              branch1._readableStreamController as ReadableStreamDefaultController<R>,\n              chunk1\n            );\n          }\n          if (!canceled2) {\n            ReadableStreamDefaultControllerEnqueue(\n              branch2._readableStreamController as ReadableStreamDefaultController<R>,\n              chunk2\n            );\n          }\n\n          reading = false;\n          if (readAgain) {\n            pullAlgorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableStreamDefaultControllerClose(branch1._readableStreamController as ReadableStreamDefaultController<R>);\n        }\n        if (!canceled2) {\n          ReadableStreamDefaultControllerClose(branch2._readableStreamController as ReadableStreamDefaultController<R>);\n        }\n\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm() {\n    // do nothing\n  }\n\n  branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm);\n  branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm);\n\n  uponRejection(reader._closedPromise, (r: any) => {\n    ReadableStreamDefaultControllerError(branch1._readableStreamController as ReadableStreamDefaultController<R>, r);\n    ReadableStreamDefaultControllerError(branch2._readableStreamController as ReadableStreamDefaultController<R>, r);\n    if (!canceled1 || !canceled2) {\n      resolveCancelPromise(undefined);\n    }\n  });\n\n  return [branch1, branch2];\n}\n\nexport function ReadableByteStreamTee(stream: ReadableByteStream): [ReadableByteStream, ReadableByteStream] {\n  assert(IsReadableStream(stream));\n  assert(IsReadableByteStreamController(stream._readableStreamController));\n\n  let reader: ReadableStreamReader<Uint8Array> = AcquireReadableStreamDefaultReader(stream);\n  let reading = false;\n  let readAgainForBranch1 = false;\n  let readAgainForBranch2 = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableByteStream;\n  let branch2: ReadableByteStream;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<void>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function forwardReaderError(thisReader: ReadableStreamReader<Uint8Array>) {\n    uponRejection(thisReader._closedPromise, r => {\n      if (thisReader !== reader) {\n        return;\n      }\n      ReadableByteStreamControllerError(branch1._readableStreamController, r);\n      ReadableByteStreamControllerError(branch2._readableStreamController, r);\n      if (!canceled1 || !canceled2) {\n        resolveCancelPromise(undefined);\n      }\n    });\n  }\n\n  function pullWithDefaultReader() {\n    if (IsReadableStreamBYOBReader(reader)) {\n      assert(reader._readIntoRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamDefaultReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const readRequest: ReadRequest<Uint8Array> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const chunk1 = chunk;\n          let chunk2 = chunk;\n          if (!canceled1 && !canceled2) {\n            try {\n              chunk2 = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(branch1._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(branch2._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n          }\n\n          if (!canceled1) {\n            ReadableByteStreamControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableByteStreamControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableByteStreamControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableByteStreamControllerClose(branch2._readableStreamController);\n        }\n        if (branch1._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch1._readableStreamController, 0);\n        }\n        if (branch2._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch2._readableStreamController, 0);\n        }\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n  }\n\n  function pullWithBYOBReader(view: ArrayBufferView, forBranch2: boolean) {\n    if (IsReadableStreamDefaultReader<Uint8Array>(reader)) {\n      assert(reader._readRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamBYOBReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const byobBranch = forBranch2 ? branch2 : branch1;\n    const otherBranch = forBranch2 ? branch1 : branch2;\n\n    const readIntoRequest: ReadIntoRequest<ArrayBufferView> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const byobCanceled = forBranch2 ? canceled2 : canceled1;\n          const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n          if (!otherCanceled) {\n            let clonedChunk;\n            try {\n              clonedChunk = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(byobBranch._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(otherBranch._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n            if (!byobCanceled) {\n              ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n            }\n            ReadableByteStreamControllerEnqueue(otherBranch._readableStreamController, clonedChunk);\n          } else if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: chunk => {\n        reading = false;\n\n        const byobCanceled = forBranch2 ? canceled2 : canceled1;\n        const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n        if (!byobCanceled) {\n          ReadableByteStreamControllerClose(byobBranch._readableStreamController);\n        }\n        if (!otherCanceled) {\n          ReadableByteStreamControllerClose(otherBranch._readableStreamController);\n        }\n\n        if (chunk !== undefined) {\n          assert(chunk.byteLength === 0);\n\n          if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n          if (!otherCanceled && otherBranch._readableStreamController._pendingPullIntos.length > 0) {\n            ReadableByteStreamControllerRespond(otherBranch._readableStreamController, 0);\n          }\n        }\n\n        if (!byobCanceled || !otherCanceled) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamBYOBReaderRead(reader, view, readIntoRequest);\n  }\n\n  function pull1Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch1 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch1._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, false);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function pull2Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch2 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch2._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, true);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm(): void {\n    return;\n  }\n\n  branch1 = CreateReadableByteStream(startAlgorithm, pull1Algorithm, cancel1Algorithm);\n  branch2 = CreateReadableByteStream(startAlgorithm, pull2Algorithm, cancel2Algorithm);\n\n  forwardReaderError(reader);\n\n  return [branch1, branch2];\n}\n", "import { assertDictionary, assertFunction, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport {\n  ReadableStreamController,\n  UnderlyingByteSource,\n  UnderlyingDefaultOrByteSource,\n  UnderlyingDefaultOrByteSourcePullCallback,\n  UnderlyingDefaultOrByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  ValidatedUnderlyingDefaultOrByteSource\n} from '../readable-stream/underlying-source';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\n\nexport function convertUnderlyingDefaultOrByteSource<R>(\n  source: UnderlyingSource<R> | UnderlyingByteSource | null,\n  context: string\n): ValidatedUnderlyingDefaultOrByteSource<R> {\n  assertDictionary(source, context);\n  const original = source as (UnderlyingDefaultOrByteSource<R> | null);\n  const autoAllocateChunkSize = original?.autoAllocateChunkSize;\n  const cancel = original?.cancel;\n  const pull = original?.pull;\n  const start = original?.start;\n  const type = original?.type;\n  return {\n    autoAllocateChunkSize: autoAllocateChunkSize === undefined ?\n      undefined :\n      convertUnsignedLongLongWithEnforceRange(\n        autoAllocateChunkSize,\n        `${context} has member 'autoAllocateChunkSize' that`\n      ),\n    cancel: cancel === undefined ?\n      undefined :\n      convertUnderlyingSourceCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    pull: pull === undefined ?\n      undefined :\n      convertUnderlyingSourcePullCallback(pull, original!, `${context} has member 'pull' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSourceStartCallback(start, original!, `${context} has member 'start' that`),\n    type: type === undefined ? undefined : convertReadableStreamType(type, `${context} has member 'type' that`)\n  };\n}\n\nfunction convertUnderlyingSourceCancelCallback(\n  fn: UnderlyingSourceCancelCallback,\n  original: UnderlyingDefaultOrByteSource,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSourcePullCallback<R>(\n  fn: UnderlyingDefaultOrByteSourcePullCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): (controller: ReadableStreamController<R>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSourceStartCallback<R>(\n  fn: UnderlyingDefaultOrByteSourceStartCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): UnderlyingDefaultOrByteSourceStartCallback<R> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertReadableStreamType(type: string, context: string): 'bytes' {\n  type = `${type}`;\n  if (type !== 'bytes') {\n    throw new TypeError(`${context} '${type}' is not a valid enumeration value for ReadableStreamType`);\n  }\n  return type;\n}\n", "import { assertDictionary } from './basic';\nimport { ReadableStreamGetReaderOptions } from '../readable-stream/reader-options';\n\nexport function convertReaderOptions(options: ReadableStreamGetReaderOptions | null | undefined,\n                                     context: string): ReadableStreamGetReaderOptions {\n  assertDictionary(options, context);\n  const mode = options?.mode;\n  return {\n    mode: mode === undefined ? undefined : convertReadableStreamReaderMode(mode, `${context} has member 'mode' that`)\n  };\n}\n\nfunction convertReadableStreamReaderMode(mode: string, context: string): 'byob' {\n  mode = `${mode}`;\n  if (mode !== 'byob') {\n    throw new TypeError(`${context} '${mode}' is not a valid enumeration value for ReadableStreamReaderMode`);\n  }\n  return mode;\n}\n", "import { assertDictionary } from './basic';\nimport {\n  ReadableStreamIteratorOptions,\n  ValidatedReadableStreamIteratorOptions\n} from '../readable-stream/iterator-options';\n\nexport function convertIteratorOptions(options: ReadableStreamIteratorOptions | null | undefined,\n                                       context: string): ValidatedReadableStreamIteratorOptions {\n  assertDictionary(options, context);\n  const preventCancel = options?.preventCancel;\n  return { preventCancel: Boolean(preventCancel) };\n}\n", "import { assertDictionary } from './basic';\nimport { StreamPipeOptions, ValidatedStreamPipeOptions } from '../readable-stream/pipe-options';\nimport { AbortSignal, isAbortSignal } from '../abort-signal';\n\nexport function convertPipeOptions(options: StreamPipeOptions | null | undefined,\n                                   context: string): ValidatedStreamPipeOptions {\n  assertDictionary(options, context);\n  const preventAbort = options?.preventAbort;\n  const preventCancel = options?.preventCancel;\n  const preventClose = options?.preventClose;\n  const signal = options?.signal;\n  if (signal !== undefined) {\n    assertAbortSignal(signal, `${context} has member 'signal' that`);\n  }\n  return {\n    preventAbort: <PERSON><PERSON><PERSON>(preventAbort),\n    preventCancel: <PERSON><PERSON>an(preventCancel),\n    preventClose: Boolean(preventClose),\n    signal\n  };\n}\n\nfunction assertAbortSignal(signal: unknown, context: string): asserts signal is AbortSignal {\n  if (!isAbortSignal(signal)) {\n    throw new TypeError(`${context} is not an AbortSignal.`);\n  }\n}\n", "import { assertDictionary, assertRequiredField } from './basic';\nimport { ReadableStream } from '../readable-stream';\nimport { WritableStream } from '../writable-stream';\nimport { assertReadableStream } from './readable-stream';\nimport { assertWritableStream } from './writable-stream';\n\nexport function convertReadableWritablePair<RS extends ReadableStream, WS extends WritableStream>(\n  pair: { readable: RS; writable: WS } | null | undefined,\n  context: string\n): { readable: RS; writable: WS } {\n  assertDictionary(pair, context);\n\n  const readable = pair?.readable;\n  assertRequiredField(readable, 'readable', 'ReadableWritablePair');\n  assertReadableStream(readable, `${context} has member 'readable' that`);\n\n  const writable = pair?.writable;\n  assertRequiredField(writable, 'writable', 'ReadableWritablePair');\n  assertWritableStream(writable, `${context} has member 'writable' that`);\n\n  return { readable, writable };\n}\n", "import assert from '../stub/assert';\nimport {\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith\n} from './helpers/webidl';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { AcquireReadableStreamAsyncIterator, ReadableStreamAsyncIterator } from './readable-stream/async-iterator';\nimport { defaultReaderClosedPromiseReject, defaultReaderClosedPromiseResolve } from './readable-stream/generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReadResult\n} from './readable-stream/default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBReadResult\n} from './readable-stream/byob-reader';\nimport { ReadableStreamPipeTo } from './readable-stream/pipe';\nimport { ReadableStreamTee } from './readable-stream/tee';\nimport { IsWritableStream, IsWritableStreamLocked, WritableStream } from './writable-stream';\nimport { SimpleQueue } from './simple-queue';\nimport {\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  SetUpReadableByteStreamController,\n  SetUpReadableByteStreamControllerFromUnderlyingSource\n} from './readable-stream/byte-stream-controller';\nimport {\n  ReadableStreamDefaultController,\n  SetUpReadableStreamDefaultController,\n  SetUpReadableStreamDefaultControllerFromUnderlyingSource\n} from './readable-stream/default-controller';\nimport {\n  UnderlyingByteSource,\n  UnderlyingByteSourcePullCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceStartCallback\n} from './readable-stream/underlying-source';\nimport { noop } from '../utils';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { CreateArrayFromList } from './abstract-ops/ecmascript';\nimport { CancelSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertUnderlyingDefaultOrByteSource } from './validators/underlying-source';\nimport { ReadableStreamGetReaderOptions } from './readable-stream/reader-options';\nimport { convertReaderOptions } from './validators/reader-options';\nimport { StreamPipeOptions, ValidatedStreamPipeOptions } from './readable-stream/pipe-options';\nimport { ReadableStreamIteratorOptions } from './readable-stream/iterator-options';\nimport { convertIteratorOptions } from './validators/iterator-options';\nimport { convertPipeOptions } from './validators/pipe-options';\nimport { ReadableWritablePair } from './readable-stream/readable-writable-pair';\nimport { convertReadableWritablePair } from './validators/readable-writable-pair';\n\nexport type ReadableByteStream = ReadableStream<Uint8Array> & {\n  _readableStreamController: ReadableByteStreamController\n};\n\ntype ReadableStreamState = 'readable' | 'closed' | 'errored';\n\n/**\n * A readable stream represents a source of data, from which you can read.\n *\n * @public\n */\nexport class ReadableStream<R = any> {\n  /** @internal */\n  _state!: ReadableStreamState;\n  /** @internal */\n  _reader: ReadableStreamReader<R> | undefined;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _disturbed!: boolean;\n  /** @internal */\n  _readableStreamController!: ReadableStreamDefaultController<R> | ReadableByteStreamController;\n\n  constructor(underlyingSource: UnderlyingByteSource, strategy?: { highWaterMark?: number; size?: undefined });\n  constructor(underlyingSource?: UnderlyingSource<R>, strategy?: QueuingStrategy<R>);\n  constructor(rawUnderlyingSource: UnderlyingSource<R> | UnderlyingByteSource | null | undefined = {},\n              rawStrategy: QueuingStrategy<R> | null | undefined = {}) {\n    if (rawUnderlyingSource === undefined) {\n      rawUnderlyingSource = null;\n    } else {\n      assertObject(rawUnderlyingSource, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, 'First parameter');\n\n    InitializeReadableStream(this);\n\n    if (underlyingSource.type === 'bytes') {\n      if (strategy.size !== undefined) {\n        throw new RangeError('The strategy for a byte stream cannot have a size function');\n      }\n      const highWaterMark = ExtractHighWaterMark(strategy, 0);\n      SetUpReadableByteStreamControllerFromUnderlyingSource(\n        this as unknown as ReadableByteStream,\n        underlyingSource,\n        highWaterMark\n      );\n    } else {\n      assert(underlyingSource.type === undefined);\n      const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n      const highWaterMark = ExtractHighWaterMark(strategy, 1);\n      SetUpReadableStreamDefaultControllerFromUnderlyingSource(\n        this,\n        underlyingSource,\n        highWaterMark,\n        sizeAlgorithm\n      );\n    }\n  }\n\n  /**\n   * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.\n   */\n  get locked(): boolean {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsReadableStreamLocked(this);\n  }\n\n  /**\n   * Cancels the stream, signaling a loss of interest in the stream by a consumer.\n   *\n   * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}\n   * method, which might or might not use it.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('cancel'));\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot cancel a stream that already has a reader'));\n    }\n\n    return ReadableStreamCancel(this, reason);\n  }\n\n  /**\n   * Creates a {@link ReadableStreamBYOBReader} and locks the stream to the new reader.\n   *\n   * This call behaves the same way as the no-argument variant, except that it only works on readable byte streams,\n   * i.e. streams which were constructed specifically with the ability to handle \"bring your own buffer\" reading.\n   * The returned BYOB reader provides the ability to directly read individual chunks from the stream via its\n   * {@link ReadableStreamBYOBReader.read | read()} method, into developer-supplied buffers, allowing more precise\n   * control over allocation.\n   */\n  getReader({ mode }: { mode: 'byob' }): ReadableStreamBYOBReader;\n  /**\n   * Creates a {@link ReadableStreamDefaultReader} and locks the stream to the new reader.\n   * While the stream is locked, no other reader can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to consume a stream\n   * in its entirety. By getting a reader for the stream, you can ensure nobody else can interleave reads with yours\n   * or cancel the stream, which would interfere with your abstraction.\n   */\n  getReader(): ReadableStreamDefaultReader<R>;\n  getReader(\n    rawOptions: ReadableStreamGetReaderOptions | null | undefined = undefined\n  ): ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('getReader');\n    }\n\n    const options = convertReaderOptions(rawOptions, 'First parameter');\n\n    if (options.mode === undefined) {\n      return AcquireReadableStreamDefaultReader(this);\n    }\n\n    assert(options.mode === 'byob');\n    return AcquireReadableStreamBYOBReader(this as unknown as ReadableByteStream);\n  }\n\n  /**\n   * Provides a convenient, chainable way of piping this readable stream through a transform stream\n   * (or any other `{ writable, readable }` pair). It simply {@link ReadableStream.pipeTo | pipes} the stream\n   * into the writable side of the supplied pair, and returns the readable side for further use.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeThrough<RS extends ReadableStream>(\n    transform: { readable: RS; writable: WritableStream<R> },\n    options?: StreamPipeOptions\n  ): RS;\n  pipeThrough<RS extends ReadableStream>(\n    rawTransform: { readable: RS; writable: WritableStream<R> } | null | undefined,\n    rawOptions: StreamPipeOptions | null | undefined = {}\n  ): RS {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('pipeThrough');\n    }\n    assertRequiredArgument(rawTransform, 1, 'pipeThrough');\n\n    const transform = convertReadableWritablePair(rawTransform, 'First parameter');\n    const options = convertPipeOptions(rawOptions, 'Second parameter');\n\n    if (IsReadableStreamLocked(this)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream');\n    }\n    if (IsWritableStreamLocked(transform.writable)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream');\n    }\n\n    const promise = ReadableStreamPipeTo(\n      this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n\n    setPromiseIsHandledToTrue(promise);\n\n    return transform.readable;\n  }\n\n  /**\n   * Pipes this readable stream to a given writable stream. The way in which the piping process behaves under\n   * various error conditions can be customized with a number of passed options. It returns a promise that fulfills\n   * when the piping process completes successfully, or rejects if any errors were encountered.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>;\n  pipeTo(destination: WritableStream<R> | null | undefined,\n         rawOptions: StreamPipeOptions | null | undefined = {}): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('pipeTo'));\n    }\n\n    if (destination === undefined) {\n      return promiseRejectedWith(`Parameter 1 is required in 'pipeTo'.`);\n    }\n    if (!IsWritableStream(destination)) {\n      return promiseRejectedWith(\n        new TypeError(`ReadableStream.prototype.pipeTo's first argument must be a WritableStream`)\n      );\n    }\n\n    let options: ValidatedStreamPipeOptions;\n    try {\n      options = convertPipeOptions(rawOptions, 'Second parameter');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream')\n      );\n    }\n    if (IsWritableStreamLocked(destination)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream')\n      );\n    }\n\n    return ReadableStreamPipeTo<R>(\n      this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n  }\n\n  /**\n   * Tees this readable stream, returning a two-element array containing the two resulting branches as\n   * new {@link ReadableStream} instances.\n   *\n   * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.\n   * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be\n   * propagated to the stream's underlying source.\n   *\n   * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,\n   * this could allow interference between the two branches.\n   */\n  tee(): [ReadableStream<R>, ReadableStream<R>] {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('tee');\n    }\n\n    const branches = ReadableStreamTee(this, false);\n    return CreateArrayFromList(branches);\n  }\n\n  /**\n   * Asynchronously iterates over the chunks in the stream's internal queue.\n   *\n   * Asynchronously iterating over the stream will lock it, preventing any other consumer from acquiring a reader.\n   * The lock will be released if the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method\n   * is called, e.g. by breaking out of the loop.\n   *\n   * By default, calling the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method will also\n   * cancel the stream. To prevent this, use the stream's {@link ReadableStream.values | values()} method, passing\n   * `true` for the `preventCancel` option.\n   */\n  values(options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n  values(rawOptions: ReadableStreamIteratorOptions | null | undefined = undefined): ReadableStreamAsyncIterator<R> {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('values');\n    }\n\n    const options = convertIteratorOptions(rawOptions, 'First parameter');\n    return AcquireReadableStreamAsyncIterator<R>(this, options.preventCancel);\n  }\n\n  /**\n   * {@inheritDoc ReadableStream.values}\n   */\n  [Symbol.asyncIterator]: (options?: ReadableStreamIteratorOptions) => ReadableStreamAsyncIterator<R>;\n}\n\nObject.defineProperties(ReadableStream.prototype, {\n  cancel: { enumerable: true },\n  getReader: { enumerable: true },\n  pipeThrough: { enumerable: true },\n  pipeTo: { enumerable: true },\n  tee: { enumerable: true },\n  values: { enumerable: true },\n  locked: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.toStringTag, {\n    value: 'ReadableStream',\n    configurable: true\n  });\n}\nif (typeof Symbol.asyncIterator === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.asyncIterator, {\n    value: ReadableStream.prototype.values,\n    writable: true,\n    configurable: true\n  });\n}\n\nexport {\n  ReadableStreamAsyncIterator,\n  ReadableStreamDefaultReadResult,\n  ReadableStreamBYOBReadResult,\n  UnderlyingByteSource,\n  UnderlyingSource,\n  UnderlyingSourceStartCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceCancelCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingByteSourcePullCallback,\n  StreamPipeOptions,\n  ReadableWritablePair,\n  ReadableStreamIteratorOptions\n};\n\n// Abstract operations for the ReadableStream.\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableStream<R>(startAlgorithm: () => void | PromiseLike<void>,\n                                        pullAlgorithm: () => Promise<void>,\n                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                        highWaterMark = 1,\n                                        sizeAlgorithm: QueuingStrategySizeCallback<R> = () => 1): ReadableStream<R> {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: ReadableStream<R> = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n\n  return stream;\n}\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableByteStream(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>\n): ReadableByteStream {\n  const stream: ReadableByteStream = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n  SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, 0, undefined);\n\n  return stream;\n}\n\nfunction InitializeReadableStream(stream: ReadableStream) {\n  stream._state = 'readable';\n  stream._reader = undefined;\n  stream._storedError = undefined;\n  stream._disturbed = false;\n}\n\nexport function IsReadableStream(x: unknown): x is ReadableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readableStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStream;\n}\n\nexport function IsReadableStreamDisturbed(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  return stream._disturbed;\n}\n\nexport function IsReadableStreamLocked(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  if (stream._reader === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamCancel<R>(stream: ReadableStream<R>, reason: any): Promise<undefined> {\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  ReadableStreamClose(stream);\n\n  const reader = stream._reader;\n  if (reader !== undefined && IsReadableStreamBYOBReader(reader)) {\n    reader._readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._closeSteps(undefined);\n    });\n    reader._readIntoRequests = new SimpleQueue();\n  }\n\n  const sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);\n  return transformPromiseWith(sourceCancelPromise, noop);\n}\n\nexport function ReadableStreamClose<R>(stream: ReadableStream<R>): void {\n  assert(stream._state === 'readable');\n\n  stream._state = 'closed';\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseResolve(reader);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    reader._readRequests.forEach(readRequest => {\n      readRequest._closeSteps();\n    });\n    reader._readRequests = new SimpleQueue();\n  }\n}\n\nexport function ReadableStreamError<R>(stream: ReadableStream<R>, e: any): void {\n  assert(IsReadableStream(stream));\n  assert(stream._state === 'readable');\n\n  stream._state = 'errored';\n  stream._storedError = e;\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseReject(reader, e);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    reader._readRequests.forEach(readRequest => {\n      readRequest._errorSteps(e);\n    });\n\n    reader._readRequests = new SimpleQueue();\n  } else {\n    assert(IsReadableStreamBYOBReader(reader));\n\n    reader._readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._errorSteps(e);\n    });\n\n    reader._readIntoRequests = new SimpleQueue();\n  }\n}\n\n// Readers\n\nexport type ReadableStreamReader<R> = ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader;\n\nexport {\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader\n};\n\n// Controllers\n\nexport {\n  ReadableStreamDefaultController,\n  ReadableStreamBYOBRequest,\n  ReadableByteStreamController\n};\n\n// Helper functions for the ReadableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStream.prototype.${name} can only be used on a ReadableStream`);\n}\n", "import { QueuingStrategyInit } from '../queuing-strategy';\nimport { assertDictionary, assertRequiredField, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategyInit(init: QueuingStrategyInit | null | undefined,\n                                           context: string): QueuingStrategyInit {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  assertRequiredField(highWaterMark, 'highWaterMark', 'QueuingStrategyInit');\n  return {\n    highWaterMark: convertUnrestrictedDouble(highWaterMark)\n  };\n}\n", "import { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst byteLengthSizeFunction = (chunk: ArrayBufferView): number => {\n  return chunk.byteLength;\n};\nObject.defineProperty(byteLengthSizeFunction, 'name', {\n  value: 'size',\n  configurable: true\n});\n\n/**\n * A queuing strategy that counts the number of bytes in each chunk.\n *\n * @public\n */\nexport default class ByteLengthQueuingStrategy implements QueuingStrategy<ArrayBufferView> {\n  /** @internal */\n  readonly _byteLengthQueuingStrategyHighWaterMark: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'ByteLengthQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('highWaterMark');\n    }\n    return this._byteLengthQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by returning the value of its `byteLength` property.\n   */\n  get size(): (chunk: ArrayBufferView) => number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('size');\n    }\n    return byteLengthSizeFunction;\n  }\n}\n\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ByteLengthQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'ByteLengthQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the ByteLengthQueuingStrategy.\n\nfunction byteLengthBrandCheckException(name: string): TypeError {\n  return new TypeError(`ByteLengthQueuingStrategy.prototype.${name} can only be used on a ByteLengthQueuingStrategy`);\n}\n\nexport function IsByteLengthQueuingStrategy(x: any): x is ByteLengthQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_byteLengthQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof ByteLengthQueuingStrategy;\n}\n", "import { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst countSizeFunction = (): 1 => {\n  return 1;\n};\nObject.defineProperty(countSizeFunction, 'name', {\n  value: 'size',\n  configurable: true\n});\n\n/**\n * A queuing strategy that counts the number of chunks.\n *\n * @public\n */\nexport default class CountQueuingStrategy implements QueuingStrategy<any> {\n  /** @internal */\n  readonly _countQueuingStrategyHighWaterMark!: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'CountQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._countQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('highWaterMark');\n    }\n    return this._countQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by always returning 1.\n   * This ensures that the total queue size is a count of the number of chunks in the queue.\n   */\n  get size(): (chunk: any) => 1 {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('size');\n    }\n    return countSizeFunction;\n  }\n}\n\nObject.defineProperties(CountQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(CountQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'CountQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the CountQueuingStrategy.\n\nfunction countBrandCheckException(name: string): TypeError {\n  return new TypeError(`CountQueuingStrategy.prototype.${name} can only be used on a CountQueuingStrategy`);\n}\n\nexport function IsCountQueuingStrategy(x: any): x is CountQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_countQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof CountQueuingStrategy;\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport {\n  Transformer,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from '../transform-stream/transformer';\nimport { TransformStreamDefaultController } from '../transform-stream';\n\nexport function convertTransformer<I, O>(original: Transformer<I, O> | null,\n                                         context: string): ValidatedTransformer<I, O> {\n  assertDictionary(original, context);\n  const flush = original?.flush;\n  const readableType = original?.readableType;\n  const start = original?.start;\n  const transform = original?.transform;\n  const writableType = original?.writableType;\n  return {\n    flush: flush === undefined ?\n      undefined :\n      convertTransformerFlushCallback(flush, original!, `${context} has member 'flush' that`),\n    readableType,\n    start: start === undefined ?\n      undefined :\n      convertTransformerStartCallback(start, original!, `${context} has member 'start' that`),\n    transform: transform === undefined ?\n      undefined :\n      convertTransformerTransformCallback(transform, original!, `${context} has member 'transform' that`),\n    writableType\n  };\n}\n\nfunction convertTransformerFlushCallback<I, O>(\n  fn: TransformerFlushCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): (controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertTransformerStartCallback<I, O>(\n  fn: TransformerStartCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): TransformerStartCallback<O> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertTransformerTransformCallback<I, O>(\n  fn: TransformerTransformCallback<I, O>,\n  original: Transformer<I, O>,\n  context: string\n): (chunk: I, controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: I, controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import assert from '../stub/assert';\nimport { newPromise, promiseRejectedWith, promiseResolvedWith, transformPromiseWith } from './helpers/webidl';\nimport { CreateReadableStream, ReadableStream, ReadableStreamDefaultController } from './readable-stream';\nimport {\n  ReadableStreamDefaultControllerCanCloseOrEnqueue,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError,\n  ReadableStreamDefaultControllerGetDesiredSize,\n  ReadableStreamDefaultControllerHasBackpressure\n} from './readable-stream/default-controller';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { CreateWritableStream, WritableStream, WritableStreamDefaultControllerErrorIfNeeded } from './writable-stream';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport {\n  Transformer,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from './transform-stream/transformer';\nimport { convertTransformer } from './validators/transformer';\n\n// Class TransformStream\n\n/**\n * A transform stream consists of a pair of streams: a {@link WritableStream | writable stream},\n * known as its writable side, and a {@link ReadableStream | readable stream}, known as its readable side.\n * In a manner specific to the transform stream in question, writes to the writable side result in new data being\n * made available for reading from the readable side.\n *\n * @public\n */\nexport class TransformStream<I = any, O = any> {\n  /** @internal */\n  _writable!: WritableStream<I>;\n  /** @internal */\n  _readable!: ReadableStream<O>;\n  /** @internal */\n  _backpressure!: boolean;\n  /** @internal */\n  _backpressureChangePromise!: Promise<void>;\n  /** @internal */\n  _backpressureChangePromise_resolve!: () => void;\n  /** @internal */\n  _transformStreamController!: TransformStreamDefaultController<O>;\n\n  constructor(\n    transformer?: Transformer<I, O>,\n    writableStrategy?: QueuingStrategy<I>,\n    readableStrategy?: QueuingStrategy<O>\n  );\n  constructor(rawTransformer: Transformer<I, O> | null | undefined = {},\n              rawWritableStrategy: QueuingStrategy<I> | null | undefined = {},\n              rawReadableStrategy: QueuingStrategy<O> | null | undefined = {}) {\n    if (rawTransformer === undefined) {\n      rawTransformer = null;\n    }\n\n    const writableStrategy = convertQueuingStrategy(rawWritableStrategy, 'Second parameter');\n    const readableStrategy = convertQueuingStrategy(rawReadableStrategy, 'Third parameter');\n\n    const transformer = convertTransformer(rawTransformer, 'First parameter');\n    if (transformer.readableType !== undefined) {\n      throw new RangeError('Invalid readableType specified');\n    }\n    if (transformer.writableType !== undefined) {\n      throw new RangeError('Invalid writableType specified');\n    }\n\n    const readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0);\n    const readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy);\n    const writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);\n    const writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);\n\n    let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n    const startPromise = newPromise<void>(resolve => {\n      startPromise_resolve = resolve;\n    });\n\n    InitializeTransformStream(\n      this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm\n    );\n    SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);\n\n    if (transformer.start !== undefined) {\n      startPromise_resolve(transformer.start(this._transformStreamController));\n    } else {\n      startPromise_resolve(undefined);\n    }\n  }\n\n  /**\n   * The readable side of the transform stream.\n   */\n  get readable(): ReadableStream<O> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('readable');\n    }\n\n    return this._readable;\n  }\n\n  /**\n   * The writable side of the transform stream.\n   */\n  get writable(): WritableStream<I> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('writable');\n    }\n\n    return this._writable;\n  }\n}\n\nObject.defineProperties(TransformStream.prototype, {\n  readable: { enumerable: true },\n  writable: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStream.prototype, Symbol.toStringTag, {\n    value: 'TransformStream',\n    configurable: true\n  });\n}\n\nexport {\n  Transformer,\n  TransformerStartCallback,\n  TransformerFlushCallback,\n  TransformerTransformCallback\n};\n\n// Transform Stream Abstract Operations\n\nexport function CreateTransformStream<I, O>(startAlgorithm: () => void | PromiseLike<void>,\n                                            transformAlgorithm: (chunk: I) => Promise<void>,\n                                            flushAlgorithm: () => Promise<void>,\n                                            writableHighWaterMark = 1,\n                                            writableSizeAlgorithm: QueuingStrategySizeCallback<I> = () => 1,\n                                            readableHighWaterMark = 0,\n                                            readableSizeAlgorithm: QueuingStrategySizeCallback<O> = () => 1) {\n  assert(IsNonNegativeNumber(writableHighWaterMark));\n  assert(IsNonNegativeNumber(readableHighWaterMark));\n\n  const stream: TransformStream<I, O> = Object.create(TransformStream.prototype);\n\n  let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n  const startPromise = newPromise<void>(resolve => {\n    startPromise_resolve = resolve;\n  });\n\n  InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark,\n                            readableSizeAlgorithm);\n\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm);\n\n  const startResult = startAlgorithm();\n  startPromise_resolve(startResult);\n  return stream;\n}\n\nfunction InitializeTransformStream<I, O>(stream: TransformStream<I, O>,\n                                         startPromise: Promise<void>,\n                                         writableHighWaterMark: number,\n                                         writableSizeAlgorithm: QueuingStrategySizeCallback<I>,\n                                         readableHighWaterMark: number,\n                                         readableSizeAlgorithm: QueuingStrategySizeCallback<O>) {\n  function startAlgorithm(): Promise<void> {\n    return startPromise;\n  }\n\n  function writeAlgorithm(chunk: I): Promise<void> {\n    return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);\n  }\n\n  function abortAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);\n  }\n\n  function closeAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSinkCloseAlgorithm(stream);\n  }\n\n  stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm,\n                                          writableHighWaterMark, writableSizeAlgorithm);\n\n  function pullAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSourcePullAlgorithm(stream);\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    TransformStreamErrorWritableAndUnblockWrite(stream, reason);\n    return promiseResolvedWith(undefined);\n  }\n\n  stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark,\n                                          readableSizeAlgorithm);\n\n  // The [[backpressure]] slot is set to undefined so that it can be initialised by TransformStreamSetBackpressure.\n  stream._backpressure = undefined!;\n  stream._backpressureChangePromise = undefined!;\n  stream._backpressureChangePromise_resolve = undefined!;\n  TransformStreamSetBackpressure(stream, true);\n\n  stream._transformStreamController = undefined!;\n}\n\nfunction IsTransformStream(x: unknown): x is TransformStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_transformStreamController')) {\n    return false;\n  }\n\n  return x instanceof TransformStream;\n}\n\n// This is a no-op if both sides are already errored.\nfunction TransformStreamError(stream: TransformStream, e: any) {\n  ReadableStreamDefaultControllerError(\n    stream._readable._readableStreamController as ReadableStreamDefaultController<any>,\n    e\n  );\n  TransformStreamErrorWritableAndUnblockWrite(stream, e);\n}\n\nfunction TransformStreamErrorWritableAndUnblockWrite(stream: TransformStream, e: any) {\n  TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController);\n  WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e);\n  if (stream._backpressure) {\n    // Pretend that pull() was called to permit any pending write() calls to complete. TransformStreamSetBackpressure()\n    // cannot be called from enqueue() or pull() once the ReadableStream is errored, so this will will be the final time\n    // _backpressure is set.\n    TransformStreamSetBackpressure(stream, false);\n  }\n}\n\nfunction TransformStreamSetBackpressure(stream: TransformStream, backpressure: boolean) {\n  // Passes also when called during construction.\n  assert(stream._backpressure !== backpressure);\n\n  if (stream._backpressureChangePromise !== undefined) {\n    stream._backpressureChangePromise_resolve();\n  }\n\n  stream._backpressureChangePromise = newPromise(resolve => {\n    stream._backpressureChangePromise_resolve = resolve;\n  });\n\n  stream._backpressure = backpressure;\n}\n\n// Class TransformStreamDefaultController\n\n/**\n * Allows control of the {@link ReadableStream} and {@link WritableStream} of the associated {@link TransformStream}.\n *\n * @public\n */\nexport class TransformStreamDefaultController<O> {\n  /** @internal */\n  _controlledTransformStream: TransformStream<any, O>;\n  /** @internal */\n  _transformAlgorithm: (chunk: any) => Promise<void>;\n  /** @internal */\n  _flushAlgorithm: () => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.\n   */\n  get desiredSize(): number | null {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    const readableController = this._controlledTransformStream._readable._readableStreamController;\n    return ReadableStreamDefaultControllerGetDesiredSize(readableController as ReadableStreamDefaultController<O>);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the readable side of the controlled transform stream.\n   */\n  enqueue(chunk: O): void;\n  enqueue(chunk: O = undefined!): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    TransformStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors both the readable side and the writable side of the controlled transform stream, making all future\n   * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.\n   */\n  error(reason: any = undefined): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    TransformStreamDefaultControllerError(this, reason);\n  }\n\n  /**\n   * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the\n   * transformer only needs to consume a portion of the chunks written to the writable side.\n   */\n  terminate(): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('terminate');\n    }\n\n    TransformStreamDefaultControllerTerminate(this);\n  }\n}\n\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  terminate: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'TransformStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Transform Stream Default Controller Abstract Operations\n\nfunction IsTransformStreamDefaultController<O = any>(x: any): x is TransformStreamDefaultController<O> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledTransformStream')) {\n    return false;\n  }\n\n  return x instanceof TransformStreamDefaultController;\n}\n\nfunction SetUpTransformStreamDefaultController<I, O>(stream: TransformStream<I, O>,\n                                                     controller: TransformStreamDefaultController<O>,\n                                                     transformAlgorithm: (chunk: I) => Promise<void>,\n                                                     flushAlgorithm: () => Promise<void>) {\n  assert(IsTransformStream(stream));\n  assert(stream._transformStreamController === undefined);\n\n  controller._controlledTransformStream = stream;\n  stream._transformStreamController = controller;\n\n  controller._transformAlgorithm = transformAlgorithm;\n  controller._flushAlgorithm = flushAlgorithm;\n}\n\nfunction SetUpTransformStreamDefaultControllerFromTransformer<I, O>(stream: TransformStream<I, O>,\n                                                                    transformer: ValidatedTransformer<I, O>) {\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  let transformAlgorithm = (chunk: I): Promise<void> => {\n    try {\n      TransformStreamDefaultControllerEnqueue(controller, chunk as unknown as O);\n      return promiseResolvedWith(undefined);\n    } catch (transformResultE) {\n      return promiseRejectedWith(transformResultE);\n    }\n  };\n\n  let flushAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (transformer.transform !== undefined) {\n    transformAlgorithm = chunk => transformer.transform!(chunk, controller);\n  }\n  if (transformer.flush !== undefined) {\n    flushAlgorithm = () => transformer.flush!(controller);\n  }\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm);\n}\n\nfunction TransformStreamDefaultControllerClearAlgorithms(controller: TransformStreamDefaultController<any>) {\n  controller._transformAlgorithm = undefined!;\n  controller._flushAlgorithm = undefined!;\n}\n\nfunction TransformStreamDefaultControllerEnqueue<O>(controller: TransformStreamDefaultController<O>, chunk: O) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController as ReadableStreamDefaultController<O>;\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController)) {\n    throw new TypeError('Readable side is not in a state that permits enqueue');\n  }\n\n  // We throttle transform invocations based on the backpressure of the ReadableStream, but we still\n  // accept TransformStreamDefaultControllerEnqueue() calls.\n\n  try {\n    ReadableStreamDefaultControllerEnqueue(readableController, chunk);\n  } catch (e) {\n    // This happens when readableStrategy.size() throws.\n    TransformStreamErrorWritableAndUnblockWrite(stream, e);\n\n    throw stream._readable._storedError;\n  }\n\n  const backpressure = ReadableStreamDefaultControllerHasBackpressure(readableController);\n  if (backpressure !== stream._backpressure) {\n    assert(backpressure);\n    TransformStreamSetBackpressure(stream, true);\n  }\n}\n\nfunction TransformStreamDefaultControllerError(controller: TransformStreamDefaultController<any>, e: any) {\n  TransformStreamError(controller._controlledTransformStream, e);\n}\n\nfunction TransformStreamDefaultControllerPerformTransform<I, O>(controller: TransformStreamDefaultController<O>,\n                                                                chunk: I) {\n  const transformPromise = controller._transformAlgorithm(chunk);\n  return transformPromiseWith(transformPromise, undefined, r => {\n    TransformStreamError(controller._controlledTransformStream, r);\n    throw r;\n  });\n}\n\nfunction TransformStreamDefaultControllerTerminate<O>(controller: TransformStreamDefaultController<O>) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController as ReadableStreamDefaultController<O>;\n\n  ReadableStreamDefaultControllerClose(readableController);\n\n  const error = new TypeError('TransformStream terminated');\n  TransformStreamErrorWritableAndUnblockWrite(stream, error);\n}\n\n// TransformStreamDefaultSink Algorithms\n\nfunction TransformStreamDefaultSinkWriteAlgorithm<I, O>(stream: TransformStream<I, O>, chunk: I): Promise<void> {\n  assert(stream._writable._state === 'writable');\n\n  const controller = stream._transformStreamController;\n\n  if (stream._backpressure) {\n    const backpressureChangePromise = stream._backpressureChangePromise;\n    assert(backpressureChangePromise !== undefined);\n    return transformPromiseWith(backpressureChangePromise, () => {\n      const writable = stream._writable;\n      const state = writable._state;\n      if (state === 'erroring') {\n        throw writable._storedError;\n      }\n      assert(state === 'writable');\n      return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n    });\n  }\n\n  return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n}\n\nfunction TransformStreamDefaultSinkAbortAlgorithm(stream: TransformStream, reason: any): Promise<void> {\n  // abort() is not called synchronously, so it is possible for abort() to be called when the stream is already\n  // errored.\n  TransformStreamError(stream, reason);\n  return promiseResolvedWith(undefined);\n}\n\nfunction TransformStreamDefaultSinkCloseAlgorithm<I, O>(stream: TransformStream<I, O>): Promise<void> {\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  const controller = stream._transformStreamController;\n  const flushPromise = controller._flushAlgorithm();\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  // Return a promise that is fulfilled with undefined on success.\n  return transformPromiseWith(flushPromise, () => {\n    if (readable._state === 'errored') {\n      throw readable._storedError;\n    }\n    ReadableStreamDefaultControllerClose(readable._readableStreamController as ReadableStreamDefaultController<O>);\n  }, r => {\n    TransformStreamError(stream, r);\n    throw readable._storedError;\n  });\n}\n\n// TransformStreamDefaultSource Algorithms\n\nfunction TransformStreamDefaultSourcePullAlgorithm(stream: TransformStream): Promise<void> {\n  // Invariant. Enforced by the promises returned by start() and pull().\n  assert(stream._backpressure);\n\n  assert(stream._backpressureChangePromise !== undefined);\n\n  TransformStreamSetBackpressure(stream, false);\n\n  // Prevent the next pull() call until there is backpressure.\n  return stream._backpressureChangePromise;\n}\n\n// Helper functions for the TransformStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStreamDefaultController.prototype.${name} can only be used on a TransformStreamDefaultController`);\n}\n\n// Helper functions for the TransformStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStream.prototype.${name} can only be used on a TransformStream`);\n}\n", "import {\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n  ReadableByteStreamController,\n  ReadableStream,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultReader,\n  TransformStream,\n  TransformStreamDefaultController,\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter\n} from './ponyfill';\nimport { globals } from './utils';\n\n// Export\nexport * from './ponyfill';\n\nconst exports = {\n  ReadableStream,\n  ReadableStreamDefaultController,\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader,\n\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter,\n\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n\n  TransformStream,\n  TransformStreamDefaultController\n};\n\n// Add classes to global scope\nif (typeof globals !== 'undefined') {\n  for (const prop in exports) {\n    if (Object.prototype.hasOwnProperty.call(exports, prop)) {\n      Object.defineProperty(globals, prop, {\n        value: exports[prop as (keyof typeof exports)],\n        writable: true,\n        configurable: true\n      });\n    }\n  }\n}\n"], "names": ["Symbol", "streamBrandCheckException", "defaultControllerBrandCheckException", "DOMException"], "mappings": ";;;AAAA;AAEA,MAAM,cAAc,GAClB,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;IACjE,MAAM;IACN,WAAW,IAAI,UAAU,WAAW,GAAoB;;ACL5D;SAEgB,IAAI;IAClB,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,UAAU;IACjB,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC/B,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACxC,OAAO,MAAM,CAAC;KACf;SAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QACxC,OAAO,MAAM,CAAC;KACf;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,MAAM,OAAO,GAAG,UAAU,EAAE;;SCdnB,YAAY,CAAC,CAAM;IACjC,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC;AAC1E,CAAC;AAEM,MAAM,8BAA8B,GAUrC,IAAI;;ACbV,MAAM,eAAe,GAAG,OAAO,CAAC;AAChC,MAAM,mBAAmB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;AACnD,MAAM,sBAAsB,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACrE,MAAM,qBAAqB,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAEnD,UAAU,CAAI,QAGrB;IACP,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC;AACvC,CAAC;SAEe,mBAAmB,CAAI,KAAyB;IAC9D,OAAO,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;SAEe,mBAAmB,CAAY,MAAW;IACxD,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;SAEe,kBAAkB,CAChC,OAAmB,EACnB,WAA4D,EAC5D,UAA8D;;;IAG9D,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAiC,CAAC;AACpG,CAAC;SAEe,WAAW,CACzB,OAAmB,EACnB,WAAoD,EACpD,UAAsD;IACtD,kBAAkB,CAChB,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,EACpD,SAAS,EACT,8BAA8B,CAC/B,CAAC;AACJ,CAAC;SAEe,eAAe,CAAI,OAAmB,EAAE,WAAmD;IACzG,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACpC,CAAC;SAEe,aAAa,CAAC,OAAyB,EAAE,UAAqD;IAC5G,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAC9C,CAAC;SAEe,oBAAoB,CAClC,OAAmB,EACnB,kBAAmE,EACnE,gBAAoE;IACpE,OAAO,kBAAkB,CAAC,OAAO,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AAC3E,CAAC;SAEe,yBAAyB,CAAC,OAAyB;IACjE,kBAAkB,CAAC,OAAO,EAAE,SAAS,EAAE,8BAA8B,CAAC,CAAC;AACzE,CAAC;AAEM,MAAM,cAAc,GAA6B,CAAC;IACvD,MAAM,oBAAoB,GAAG,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC;IAC/D,IAAI,OAAO,oBAAoB,KAAK,UAAU,EAAE;QAC9C,OAAO,oBAAoB,CAAC;KAC7B;IAED,MAAM,eAAe,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACvD,OAAO,CAAC,EAAc,KAAK,kBAAkB,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;AACrE,CAAC,GAAG,CAAC;SAEW,WAAW,CAAwB,CAA+B,EAAE,CAAI,EAAE,IAAO;IAC/F,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE;QAC3B,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;KACnD;IACD,OAAO,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,CAAC;SAEe,WAAW,CAAwB,CAAgD,EAChD,CAAI,EACJ,IAAO;IAIxD,IAAI;QACF,OAAO,mBAAmB,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KACrD;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,mBAAmB,CAAC,KAAK,CAAC,CAAC;KACnC;AACH;;ACzFA;AACA;AAEA,MAAM,oBAAoB,GAAG,KAAK,CAAC;AAOnC;;;;;;MAMa,WAAW;IAMtB;QAHQ,YAAO,GAAG,CAAC,CAAC;QACZ,UAAK,GAAG,CAAC,CAAC;;QAIhB,IAAI,CAAC,MAAM,GAAG;YACZ,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,SAAS;SACjB,CAAC;QACF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;;;;QAIzB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;;QAEjB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;KAChB;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;;;;IAMD,IAAI,CAAC,OAAU;QACb,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;QAC3B,IAAI,OAAO,GAAG,OAAO,CACe;QACpC,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,oBAAoB,GAAG,CAAC,EAAE;YACzD,OAAO,GAAG;gBACR,SAAS,EAAE,EAAE;gBACb,KAAK,EAAE,SAAS;aACjB,CAAC;SACH;;;QAID,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,OAAO,KAAK,OAAO,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;YACrB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC;SACzB;QACD,EAAE,IAAI,CAAC,KAAK,CAAC;KACd;;;IAID,KAAK;QAGH,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAI,QAAQ,GAAG,QAAQ,CAAC;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;QAC/B,IAAI,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;QAE9B,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC;QACpC,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAEpC,IAAI,SAAS,KAAK,oBAAoB,EAAE;YAGtC,QAAQ,GAAG,QAAQ,CAAC,KAAM,CAAC;YAC3B,SAAS,GAAG,CAAC,CAAC;SACf;;QAGD,EAAE,IAAI,CAAC,KAAK,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,IAAI,QAAQ,KAAK,QAAQ,EAAE;YACzB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;SACxB;;QAGD,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAU,CAAC;QAEjC,OAAO,OAAO,CAAC;KAChB;;;;;;;;;IAUD,OAAO,CAAC,QAA8B;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;QACrB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACvB,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAC9B,OAAO,CAAC,KAAK,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YACxD,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,EAAE;gBAGzB,IAAI,GAAG,IAAI,CAAC,KAAM,CAAC;gBACnB,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC1B,CAAC,GAAG,CAAC,CAAC;gBACN,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,MAAM;iBACP;aACF;YACD,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,EAAE,CAAC,CAAC;SACL;KACF;;;IAID,IAAI;QAGF,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KAChC;;;SCrIa,qCAAqC,CAAI,MAA+B,EAAE,MAAyB;IACjH,MAAM,CAAC,oBAAoB,GAAG,MAAM,CAAC;IACrC,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;IAExB,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,oCAAoC,CAAC,MAAM,CAAC,CAAC;KAC9C;SAAM,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QACrC,8CAA8C,CAAC,MAAM,CAAC,CAAC;KACxD;SAAM;QAGL,8CAA8C,CAAC,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;KAC7E;AACH,CAAC;AAED;AACA;SAEgB,iCAAiC,CAAC,MAAiC,EAAE,MAAW;IAC9F,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CACb;IAC7B,OAAO,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC;SAEe,kCAAkC,CAAC,MAAiC;IAIlF,IAAI,MAAM,CAAC,oBAAoB,CAAC,MAAM,KAAK,UAAU,EAAE;QACrD,gCAAgC,CAC9B,MAAM,EACN,IAAI,SAAS,CAAC,kFAAkF,CAAC,CAAC,CAAC;KACtG;SAAM;QACL,yCAAyC,CACvC,MAAM,EACN,IAAI,SAAS,CAAC,kFAAkF,CAAC,CAAC,CAAC;KACtG;IAED,MAAM,CAAC,oBAAoB,CAAC,OAAO,GAAG,SAAS,CAAC;IAChD,MAAM,CAAC,oBAAoB,GAAG,SAAU,CAAC;AAC3C,CAAC;AAED;SAEgB,mBAAmB,CAAC,IAAY;IAC9C,OAAO,IAAI,SAAS,CAAC,SAAS,GAAG,IAAI,GAAG,mCAAmC,CAAC,CAAC;AAC/E,CAAC;AAED;SAEgB,oCAAoC,CAAC,MAAiC;IACpF,MAAM,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,OAAO,EAAE,MAAM;QACjD,MAAM,CAAC,sBAAsB,GAAG,OAAO,CAAC;QACxC,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC;KACvC,CAAC,CAAC;AACL,CAAC;SAEe,8CAA8C,CAAC,MAAiC,EAAE,MAAW;IAC3G,oCAAoC,CAAC,MAAM,CAAC,CAAC;IAC7C,gCAAgC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACnD,CAAC;SAEe,8CAA8C,CAAC,MAAiC;IAC9F,oCAAoC,CAAC,MAAM,CAAC,CAAC;IAC7C,iCAAiC,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;SAEe,gCAAgC,CAAC,MAAiC,EAAE,MAAW;IAC7F,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KACR;IAED,yBAAyB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACjD,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;IAC1C,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC3C,CAAC;SAEe,yCAAyC,CAAC,MAAiC,EAAE,MAAW;IAItG,8CAA8C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACjE,CAAC;SAEe,iCAAiC,CAAC,MAAiC;IACjF,IAAI,MAAM,CAAC,sBAAsB,KAAK,SAAS,EAAE;QAC/C,OAAO;KACR;IAED,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;IACzC,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;IAC1C,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC3C;;ACjGO,MAAM,UAAU,GAAGA,cAAM,CAAC,gBAAgB,CAAC,CAAC;AAC5C,MAAM,UAAU,GAAGA,cAAM,CAAC,gBAAgB,CAAC,CAAC;AAC5C,MAAM,WAAW,GAAGA,cAAM,CAAC,iBAAiB,CAAC,CAAC;AAC9C,MAAM,SAAS,GAAGA,cAAM,CAAC,eAAe,CAAC;;ACHhD;AAEA;AACA,MAAM,cAAc,GAA2B,MAAM,CAAC,QAAQ,IAAI,UAAU,CAAC;IAC3E,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;;ACLD;AAEA;AACA,MAAM,SAAS,GAAsB,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC;IAC5D,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;;ACFD;SACgB,YAAY,CAAC,CAAM;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;AAC1D,CAAC;SAEe,gBAAgB,CAAC,GAAY,EACZ,OAAe;IAC9C,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,oBAAoB,CAAC,CAAC;KACrD;AACH,CAAC;AAID;SACgB,cAAc,CAAC,CAAU,EAAE,OAAe;IACxD,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE;QAC3B,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,qBAAqB,CAAC,CAAC;KACtD;AACH,CAAC;AAED;SACgB,QAAQ,CAAC,CAAM;IAC7B,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC;AAC1E,CAAC;SAEe,YAAY,CAAC,CAAU,EACV,OAAe;IAC1C,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAChB,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,oBAAoB,CAAC,CAAC;KACrD;AACH,CAAC;SAEe,sBAAsB,CAAgB,CAAgB,EAChB,QAAgB,EAChB,OAAe;IACnE,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,MAAM,IAAI,SAAS,CAAC,aAAa,QAAQ,oBAAoB,OAAO,IAAI,CAAC,CAAC;KAC3E;AACH,CAAC;SAEe,mBAAmB,CAAgB,CAAgB,EAChB,KAAa,EACb,OAAe;IAChE,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,MAAM,IAAI,SAAS,CAAC,GAAG,KAAK,oBAAoB,OAAO,IAAI,CAAC,CAAC;KAC9D;AACH,CAAC;AAED;SACgB,yBAAyB,CAAC,KAAc;IACtD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAED,SAAS,kBAAkB,CAAC,CAAS;IACnC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,WAAW,CAAC,CAAS;IAC5B,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED;SACgB,uCAAuC,CAAC,KAAc,EAAE,OAAe;IACrF,MAAM,UAAU,GAAG,CAAC,CAAC;IACrB,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAE3C,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;IAE1B,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;QACtB,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,yBAAyB,CAAC,CAAC;KAC1D;IAED,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnB,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,UAAU,EAAE;QACpC,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,qCAAqC,UAAU,OAAO,UAAU,aAAa,CAAC,CAAC;KAC9G;IAED,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACjC,OAAO,CAAC,CAAC;KACV;;;;;IAOD,OAAO,CAAC,CAAC;AACX;;SC3FgB,oBAAoB,CAAC,CAAU,EAAE,OAAe;IAC9D,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;QACxB,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,2BAA2B,CAAC,CAAC;KAC5D;AACH;;ACsBA;SAEgB,kCAAkC,CAAI,MAAsB;IAC1E,OAAO,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC;AAED;SAEgB,4BAA4B,CAAI,MAAyB,EACzB,WAA2B;IAIxE,MAAM,CAAC,OAA2C,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACtF,CAAC;SAEe,gCAAgC,CAAI,MAAyB,EAAE,KAAoB,EAAE,IAAa;IAChH,MAAM,MAAM,GAAG,MAAM,CAAC,OAAyC,CAEvB;IAExC,MAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,EAAG,CAAC;IAClD,IAAI,IAAI,EAAE;QACR,WAAW,CAAC,WAAW,EAAE,CAAC;KAC3B;SAAM;QACL,WAAW,CAAC,WAAW,CAAC,KAAM,CAAC,CAAC;KACjC;AACH,CAAC;SAEe,gCAAgC,CAAI,MAAyB;IAC3E,OAAQ,MAAM,CAAC,OAA0C,CAAC,aAAa,CAAC,MAAM,CAAC;AACjF,CAAC;SAEe,8BAA8B,CAAC,MAAsB;IACnE,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAE9B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;QAC1C,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAYD;;;;;MAKa,2BAA2B;IAYtC,YAAY,MAAyB;QACnC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,6BAA6B,CAAC,CAAC;QACjE,oBAAoB,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAEhD,IAAI,sBAAsB,CAAC,MAAM,CAAC,EAAE;YAClC,MAAM,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;SACpG;QAED,qCAAqC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEpD,IAAI,CAAC,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC;KACxC;;;;;IAMD,IAAI,MAAM;QACR,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;SACxE;QAED,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;;;;IAKD,MAAM,CAAC,SAAc,SAAS;QAC5B,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;SACxE;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3D;QAED,OAAO,iCAAiC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACxD;;;;;;IAOD,IAAI;QACF,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;SAC9D;QAED,IAAI,cAAqE,CAAC;QAC1E,IAAI,aAAqC,CAAC;QAC1C,MAAM,OAAO,GAAG,UAAU,CAAqC,CAAC,OAAO,EAAE,MAAM;YAC7E,cAAc,GAAG,OAAO,CAAC;YACzB,aAAa,GAAG,MAAM,CAAC;SACxB,CAAC,CAAC;QACH,MAAM,WAAW,GAAmB;YAClC,WAAW,EAAE,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YACnE,WAAW,EAAE,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YACnE,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC;SACnC,CAAC;QACF,+BAA+B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QACnD,OAAO,OAAO,CAAC;KAChB;;;;;;;;;;IAWD,WAAW;QACT,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,gCAAgC,CAAC,aAAa,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO;SACR;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,MAAM,IAAI,SAAS,CAAC,qFAAqF,CAAC,CAAC;SAC5G;QAED,kCAAkC,CAAC,IAAI,CAAC,CAAC;KAC1C;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,SAAS,EAAE;IAC7D,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC5B,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC1B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACjC,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC7B,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,2BAA2B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAC/E,KAAK,EAAE,6BAA6B;QACpC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;SAEgB,6BAA6B,CAAU,CAAM;IAC3D,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC,EAAE;QAC7D,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,2BAA2B,CAAC;AAClD,CAAC;SAEe,+BAA+B,CAAI,MAAsC,EACtC,WAA2B;IAC5E,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;IAE7B,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;IAEzB,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC9B,WAAW,CAAC,WAAW,EAAE,CAAC;KAC3B;SAAM,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;QACtC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAC9C;SAAM;QAEL,MAAM,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,WAA+B,CAAC,CAAC;KAC9E;AACH,CAAC;AAED;AAEA,SAAS,gCAAgC,CAAC,IAAY;IACpD,OAAO,IAAI,SAAS,CAClB,yCAAyC,IAAI,oDAAoD,CAAC,CAAC;AACvG;;ACvPA;AAEA;AACO,MAAM,sBAAsB,GACjC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,sBAAiD,CAAC,CAAC,SAAS,CAAC;;ACJ3G;MAqCa,+BAA+B;IAM1C,YAAY,MAAsC,EAAE,aAAsB;QAHlE,oBAAe,GAA4D,SAAS,CAAC;QACrF,gBAAW,GAAG,KAAK,CAAC;QAG1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;KACrC;IAED,IAAI;QACF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;YACzC,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,CAAC;YAChE,SAAS,EAAE,CAAC;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IAED,MAAM,CAAC,KAAU;QACf,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,eAAe;YACzB,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE,WAAW,CAAC;YACpE,WAAW,EAAE,CAAC;KACjB;IAEO,UAAU;QAChB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;SAC1D;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC7C,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC;SAC5D;QAED,IAAI,cAAqE,CAAC;QAC1E,IAAI,aAAqC,CAAC;QAC1C,MAAM,OAAO,GAAG,UAAU,CAAqC,CAAC,OAAO,EAAE,MAAM;YAC7E,cAAc,GAAG,OAAO,CAAC;YACzB,aAAa,GAAG,MAAM,CAAC;SACxB,CAAC,CAAC;QACH,MAAM,WAAW,GAAmB;YAClC,WAAW,EAAE,KAAK;gBAChB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;;;gBAGjC,cAAc,CAAC,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;aACrE;YACD,WAAW,EAAE;gBACX,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;gBACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,kCAAkC,CAAC,MAAM,CAAC,CAAC;gBAC3C,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;aAClD;YACD,WAAW,EAAE,MAAM;gBACjB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;gBACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,kCAAkC,CAAC,MAAM,CAAC,CAAC;gBAC3C,aAAa,CAAC,MAAM,CAAC,CAAC;aACvB;SACF,CAAC;QACF,+BAA+B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACrD,OAAO,OAAO,CAAC;KAChB;IAEO,YAAY,CAAC,KAAU;QAC7B,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;SAC/C;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC7C,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,CAAC;SAG5B;QAE1C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,MAAM,MAAM,GAAG,iCAAiC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAChE,kCAAkC,CAAC,MAAM,CAAC,CAAC;YAC3C,OAAO,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;SACpE;QAED,kCAAkC,CAAC,MAAM,CAAC,CAAC;QAC3C,OAAO,mBAAmB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;KACnD;CACF;AAWD,MAAM,oCAAoC,GAA6C;IACrF,IAAI;QACF,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,sCAAsC,CAAC,MAAM,CAAC,CAAC,CAAC;SAC5E;QACD,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;KACvC;IAED,MAAM,CAAiD,KAAU;QAC/D,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,sCAAsC,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9E;QACD,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC9C;CACK,CAAC;AACT,IAAI,sBAAsB,KAAK,SAAS,EAAE;IACxC,MAAM,CAAC,cAAc,CAAC,oCAAoC,EAAE,sBAAsB,CAAC,CAAC;CACrF;AAED;SAEgB,kCAAkC,CAAI,MAAyB,EACzB,aAAsB;IAC1E,MAAM,MAAM,GAAG,kCAAkC,CAAI,MAAM,CAAC,CAAC;IAC7D,MAAM,IAAI,GAAG,IAAI,+BAA+B,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IACxE,MAAM,QAAQ,GAA2C,MAAM,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC;IAC7G,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,6BAA6B,CAAU,CAAM;IACpD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,oBAAoB,CAAC,EAAE;QAClE,OAAO,KAAK,CAAC;KACd;IAED,IAAI;;QAEF,OAAQ,CAA8C,CAAC,kBAAkB;YACvE,+BAA+B,CAAC;KACnC;IAAC,WAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;AAEA,SAAS,sCAAsC,CAAC,IAAY;IAC1D,OAAO,IAAI,SAAS,CAAC,+BAA+B,IAAI,mDAAmD,CAAC,CAAC;AAC/G;;AC5LA;AAEA;AACA,MAAM,WAAW,GAAwB,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC;;IAElE,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;;SCNe,mBAAmB,CAAkB,QAAW;;;IAG9D,OAAO,QAAQ,CAAC,KAAK,EAAO,CAAC;AAC/B,CAAC;SAEe,kBAAkB,CAAC,IAAiB,EACjB,UAAkB,EAClB,GAAgB,EAChB,SAAiB,EACjB,CAAS;IAC1C,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC1E,CAAC;AAED;SACgB,mBAAmB,CAA4B,CAAI;IACjE,OAAO,CAAC,CAAC;AACX,CAAC;AAQD;AACA;SACgB,gBAAgB,CAAC,CAAkB;IACjD,OAAO,KAAK,CAAC;AACf,CAAC;SAEe,gBAAgB,CAAC,MAAuB,EAAE,KAAa,EAAE,GAAW;;;IAGlF,IAAI,MAAM,CAAC,KAAK,EAAE;QAChB,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACjC;IACD,MAAM,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC;IAC3B,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACtC,kBAAkB,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACpD,OAAO,KAAK,CAAC;AACf;;SCtCgB,mBAAmB,CAAC,CAAS;IAC3C,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE;QAClB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,GAAG,CAAC,EAAE;QACT,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;SAEe,iBAAiB,CAAC,CAAkB;IAClD,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;IACrF,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC;;SCRgB,YAAY,CAAI,SAAuC;IAIrE,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,EAAG,CAAC;IACvC,SAAS,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC;IACvC,IAAI,SAAS,CAAC,eAAe,GAAG,CAAC,EAAE;QACjC,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;KAC/B;IAED,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,CAAC;SAEe,oBAAoB,CAAI,SAAuC,EAAE,KAAQ,EAAE,IAAY;IAGrG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,EAAE;QACnD,MAAM,IAAI,UAAU,CAAC,sDAAsD,CAAC,CAAC;KAC9E;IAED,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACvC,SAAS,CAAC,eAAe,IAAI,IAAI,CAAC;AACpC,CAAC;SAEe,cAAc,CAAI,SAAuC;IAIvE,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACrC,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,CAAC;SAEe,UAAU,CAAI,SAA4B;IAGxD,SAAS,CAAC,MAAM,GAAG,IAAI,WAAW,EAAK,CAAC;IACxC,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;AAChC;;ACdA;;;;;MAKa,yBAAyB;IAMpC;QACE,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;;;;IAKD,IAAI,IAAI;QACN,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,8BAA8B,CAAC,MAAM,CAAC,CAAC;SAC9C;QAED,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAUD,OAAO,CAAC,YAAgC;QACtC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,8BAA8B,CAAC,SAAS,CAAC,CAAC;SACjD;QACD,sBAAsB,CAAC,YAAY,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;QACnD,YAAY,GAAG,uCAAuC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;QAExF,IAAI,IAAI,CAAC,uCAAuC,KAAK,SAAS,EAAE;YAC9D,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;SAC/D;QAED,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC,EAAE,CAKA;QAE1C,mCAAmC,CAAC,IAAI,CAAC,uCAAuC,EAAE,YAAY,CAAC,CAAC;KACjG;IAUD,kBAAkB,CAAC,IAAiC;QAClD,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,8BAA8B,CAAC,oBAAoB,CAAC,CAAC;SAC5D;QACD,sBAAsB,CAAC,IAAI,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC;QAEtD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC7B,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;SACrE;QAED,IAAI,IAAI,CAAC,uCAAuC,KAAK,SAAS,EAAE;YAC9D,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;SAC/D;QAED,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAElC;QAED,8CAA8C,CAAC,IAAI,CAAC,uCAAuC,EAAE,IAAI,CAAC,CAAC;KACpG;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,SAAS,EAAE;IAC3D,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC7B,kBAAkB,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACxC,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC3B,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,yBAAyB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAC7E,KAAK,EAAE,2BAA2B;QAClC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAyCD;;;;;MAKa,4BAA4B;IA4BvC;QACE,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;;;;IAKD,IAAI,WAAW;QACb,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACzC,MAAM,uCAAuC,CAAC,aAAa,CAAC,CAAC;SAC9D;QAED,OAAO,0CAA0C,CAAC,IAAI,CAAC,CAAC;KACzD;;;;;IAMD,IAAI,WAAW;QACb,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACzC,MAAM,uCAAuC,CAAC,aAAa,CAAC,CAAC;SAC9D;QAED,OAAO,0CAA0C,CAAC,IAAI,CAAC,CAAC;KACzD;;;;;IAMD,KAAK;QACH,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACzC,MAAM,uCAAuC,CAAC,OAAO,CAAC,CAAC;SACxD;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,MAAM,IAAI,SAAS,CAAC,4DAA4D,CAAC,CAAC;SACnF;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC;QACxD,IAAI,KAAK,KAAK,UAAU,EAAE;YACxB,MAAM,IAAI,SAAS,CAAC,kBAAkB,KAAK,2DAA2D,CAAC,CAAC;SACzG;QAED,iCAAiC,CAAC,IAAI,CAAC,CAAC;KACzC;IAOD,OAAO,CAAC,KAAkC;QACxC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACzC,MAAM,uCAAuC,CAAC,SAAS,CAAC,CAAC;SAC1D;QAED,sBAAsB,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9B,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;SAC3D;QACD,IAAI,KAAK,CAAC,UAAU,KAAK,CAAC,EAAE;YAC1B,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;SAC5D;QACD,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE;YACjC,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;SACrE;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;SACrD;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC;QACxD,IAAI,KAAK,KAAK,UAAU,EAAE;YACxB,MAAM,IAAI,SAAS,CAAC,kBAAkB,KAAK,gEAAgE,CAAC,CAAC;SAC9G;QAED,mCAAmC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAClD;;;;IAKD,KAAK,CAAC,IAAS,SAAS;QACtB,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACzC,MAAM,uCAAuC,CAAC,OAAO,CAAC,CAAC;SACxD;QAED,iCAAiC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC5C;;IAGD,CAAC,WAAW,CAAC,CAAC,MAAW;QACvB,iDAAiD,CAAC,IAAI,CAAC,CAAC;QAExD,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7C,2CAA2C,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC;KACf;;IAGD,CAAC,SAAS,CAAC,CAAC,WAAoC;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,6BAA6B,CACF;QAE/C,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE;YAG5B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAG,CAAC;YACnC,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,UAAU,CAAC;YAEzC,4CAA4C,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;YAE9E,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC9B,OAAO;SACR;QAED,MAAM,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC1D,IAAI,qBAAqB,KAAK,SAAS,EAAE;YACvC,IAAI,MAAmB,CAAC;YACxB,IAAI;gBACF,MAAM,GAAG,IAAI,WAAW,CAAC,qBAAqB,CAAC,CAAC;aACjD;YAAC,OAAO,OAAO,EAAE;gBAChB,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACjC,OAAO;aACR;YAED,MAAM,kBAAkB,GAA8B;gBACpD,MAAM;gBACN,gBAAgB,EAAE,qBAAqB;gBACvC,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,qBAAqB;gBACjC,WAAW,EAAE,CAAC;gBACd,WAAW,EAAE,CAAC;gBACd,eAAe,EAAE,UAAU;gBAC3B,UAAU,EAAE,SAAS;aACtB,CAAC;YAEF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACjD;QAED,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAClD,4CAA4C,CAAC,IAAI,CAAC,CAAC;KACpD;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,SAAS,EAAE;IAC9D,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC7B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACjC,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAClC,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,4BAA4B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAChF,KAAK,EAAE,8BAA8B;QACrC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;SAEgB,8BAA8B,CAAC,CAAM;IACnD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,+BAA+B,CAAC,EAAE;QAC7E,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,4BAA4B,CAAC;AACnD,CAAC;AAED,SAAS,2BAA2B,CAAC,CAAM;IACzC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,yCAAyC,CAAC,EAAE;QACvF,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,yBAAyB,CAAC;AAChD,CAAC;AAED,SAAS,4CAA4C,CAAC,UAAwC;IAC5F,MAAM,UAAU,GAAG,0CAA0C,CAAC,UAAU,CAAC,CAAC;IAC1E,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;IAED,IAAI,UAAU,CAAC,QAAQ,EAAE;QACvB,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,OAAO;KAGsB;IAE/B,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;;IAG3B,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;IAChD,WAAW,CACT,WAAW,EACX;QACE,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;QAE5B,IAAI,UAAU,CAAC,UAAU,EAAE;YACzB,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;YAC9B,4CAA4C,CAAC,UAAU,CAAC,CAAC;SAC1D;KACF,EACD,CAAC;QACC,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;KAClD,CACF,CAAC;AACJ,CAAC;AAED,SAAS,iDAAiD,CAAC,UAAwC;IACjG,iDAAiD,CAAC,UAAU,CAAC,CAAC;IAC9D,UAAU,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AACnD,CAAC;AAED,SAAS,oDAAoD,CAC3D,MAA0B,EAC1B,kBAAyC;IAIzC,IAAI,IAAI,GAAG,KAAK,CAAC;IACjB,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QAE9B,IAAI,GAAG,IAAI,CAAC;KACb;IAED,MAAM,UAAU,GAAG,qDAAqD,CAAI,kBAAkB,CAAC,CAAC;IAChG,IAAI,kBAAkB,CAAC,UAAU,KAAK,SAAS,EAAE;QAC/C,gCAAgC,CAAC,MAAM,EAAE,UAAmC,EAAE,IAAI,CAAC,CAAC;KACrF;SAAM;QAEL,oCAAoC,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;KAChE;AACH,CAAC;AAED,SAAS,qDAAqD,CAC5D,kBAAyC;IAEzC,MAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC;IACnD,MAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAGV;IAExC,OAAO,IAAI,kBAAkB,CAAC,eAAe,CAC3C,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,UAAU,EAAE,WAAW,GAAG,WAAW,CAAM,CAAC;AAC9F,CAAC;AAED,SAAS,+CAA+C,CAAC,UAAwC,EACxC,MAAuB,EACvB,UAAkB,EAClB,UAAkB;IACzE,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;IAC3D,UAAU,CAAC,eAAe,IAAI,UAAU,CAAC;AAC3C,CAAC;AAED,SAAS,2DAA2D,CAAC,UAAwC,EACxC,kBAAsC;IACzG,MAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC;IAEnD,MAAM,mBAAmB,GAAG,kBAAkB,CAAC,WAAW,GAAG,kBAAkB,CAAC,WAAW,GAAG,WAAW,CAAC;IAE1G,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,eAAe,EAC1B,kBAAkB,CAAC,UAAU,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;IAChG,MAAM,cAAc,GAAG,kBAAkB,CAAC,WAAW,GAAG,cAAc,CAAC;IACvE,MAAM,eAAe,GAAG,cAAc,GAAG,cAAc,GAAG,WAAW,CAAC;IAEtE,IAAI,yBAAyB,GAAG,cAAc,CAAC;IAC/C,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,IAAI,eAAe,GAAG,mBAAmB,EAAE;QACzC,yBAAyB,GAAG,eAAe,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC7E,KAAK,GAAG,IAAI,CAAC;KACd;IAED,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;IAEhC,OAAO,yBAAyB,GAAG,CAAC,EAAE;QACpC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAEjC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;QAEhF,MAAM,SAAS,GAAG,kBAAkB,CAAC,UAAU,GAAG,kBAAkB,CAAC,WAAW,CAAC;QACjF,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAElH,IAAI,WAAW,CAAC,UAAU,KAAK,WAAW,EAAE;YAC1C,KAAK,CAAC,KAAK,EAAE,CAAC;SACf;aAAM;YACL,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC;YACtC,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC;SACvC;QACD,UAAU,CAAC,eAAe,IAAI,WAAW,CAAC;QAE1C,sDAAsD,CAAC,UAAU,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;QAEpG,yBAAyB,IAAI,WAAW,CAAC;KAC1C;IAQD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sDAAsD,CAAC,UAAwC,EACxC,IAAY,EACZ,kBAAsC;IAGpG,kBAAkB,CAAC,WAAW,IAAI,IAAI,CAAC;AACzC,CAAC;AAED,SAAS,4CAA4C,CAAC,UAAwC;IAG5F,IAAI,UAAU,CAAC,eAAe,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,EAAE;QAClE,2CAA2C,CAAC,UAAU,CAAC,CAAC;QACxD,mBAAmB,CAAC,UAAU,CAAC,6BAA6B,CAAC,CAAC;KAC/D;SAAM;QACL,4CAA4C,CAAC,UAAU,CAAC,CAAC;KAC1D;AACH,CAAC;AAED,SAAS,iDAAiD,CAAC,UAAwC;IACjG,IAAI,UAAU,CAAC,YAAY,KAAK,IAAI,EAAE;QACpC,OAAO;KACR;IAED,UAAU,CAAC,YAAY,CAAC,uCAAuC,GAAG,SAAU,CAAC;IAC7E,UAAU,CAAC,YAAY,CAAC,KAAK,GAAG,IAAK,CAAC;IACtC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;AACjC,CAAC;AAED,SAAS,gEAAgE,CAAC,UAAwC;IAGhH,OAAO,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC9C,IAAI,UAAU,CAAC,eAAe,KAAK,CAAC,EAAE;YACpC,OAAO;SACR;QAED,MAAM,kBAAkB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAE/D,IAAI,2DAA2D,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE;YAC/F,gDAAgD,CAAC,UAAU,CAAC,CAAC;YAE7D,oDAAoD,CAClD,UAAU,CAAC,6BAA6B,EACxC,kBAAkB,CACnB,CAAC;SACH;KACF;AACH,CAAC;SAEe,oCAAoC,CAClD,UAAwC,EACxC,IAAO,EACP,eAAmC;IAEnC,MAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IAExD,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;QACjC,WAAW,GAAI,IAAI,CAAC,WAA6C,CAAC,iBAAiB,CAAC;KACrF;IAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAA4C,CAAC;;IAG/D,MAAM,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;;;IAMhD,MAAM,kBAAkB,GAA8B;QACpD,MAAM;QACN,gBAAgB,EAAE,MAAM,CAAC,UAAU;QACnC,UAAU,EAAE,IAAI,CAAC,UAAU;QAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;QAC3B,WAAW,EAAE,CAAC;QACd,WAAW;QACX,eAAe,EAAE,IAAI;QACrB,UAAU,EAAE,MAAM;KACnB,CAAC;IAEF,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3C,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;QAMtD,gCAAgC,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAC1D,OAAO;KACR;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACxF,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO;KACR;IAED,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,EAAE;QAClC,IAAI,2DAA2D,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE;YAC/F,MAAM,UAAU,GAAG,qDAAqD,CAAI,kBAAkB,CAAC,CAAC;YAEhG,4CAA4C,CAAC,UAAU,CAAC,CAAC;YAEzD,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACxC,OAAO;SACR;QAED,IAAI,UAAU,CAAC,eAAe,EAAE;YAC9B,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;YACnF,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAEjD,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/B,OAAO;SACR;KACF;IAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAEtD,gCAAgC,CAAI,MAAM,EAAE,eAAe,CAAC,CAAC;IAC7D,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,gDAAgD,CAAC,UAAwC,EACxC,eAAmC;IAG3F,MAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IACxD,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE;QACvC,OAAO,oCAAoC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACvD,MAAM,kBAAkB,GAAG,gDAAgD,CAAC,UAAU,CAAC,CAAC;YACxF,oDAAoD,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;SAClF;KACF;AACH,CAAC;AAED,SAAS,kDAAkD,CAAC,UAAwC,EACxC,YAAoB,EACpB,kBAAsC;IAGhG,sDAAsD,CAAC,UAAU,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;IAErG,IAAI,kBAAkB,CAAC,WAAW,GAAG,kBAAkB,CAAC,WAAW,EAAE;QACnE,OAAO;KACR;IAED,gDAAgD,CAAC,UAAU,CAAC,CAAC;IAE7D,MAAM,aAAa,GAAG,kBAAkB,CAAC,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC;IACtF,IAAI,aAAa,GAAG,CAAC,EAAE;QACrB,MAAM,GAAG,GAAG,kBAAkB,CAAC,UAAU,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC3E,MAAM,SAAS,GAAG,gBAAgB,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,GAAG,aAAa,EAAE,GAAG,CAAC,CAAC;QACxF,+CAA+C,CAAC,UAAU,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;KACjG;IAED,kBAAkB,CAAC,WAAW,IAAI,aAAa,CAAC;IAChD,oDAAoD,CAAC,UAAU,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,CAAC;IAEnH,gEAAgE,CAAC,UAAU,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,2CAA2C,CAAC,UAAwC,EAAE,YAAoB;IACjH,MAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CACJ;IAEvD,iDAAiD,CAAC,UAAU,CAAC,CAAC;IAE9D,MAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;IAC9D,IAAI,KAAK,KAAK,QAAQ,EAAE;QAEtB,gDAAgD,CAAC,UAA2B,CAAC,CAAC;KAC/E;SAAM;QAGL,kDAAkD,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;KAC/F;IAED,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,gDAAgD,CACvD,UAAwC;IAGxC,MAAM,UAAU,GAAG,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAG,CAAC;IACzD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,0CAA0C,CAAC,UAAwC;IAC1F,MAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IAExD,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO,KAAK,CAAC;KACd;IAED,IAAI,UAAU,CAAC,eAAe,EAAE;QAC9B,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,8BAA8B,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;QAC1F,OAAO,IAAI,CAAC;KACb;IAED,IAAI,2BAA2B,CAAC,MAAM,CAAC,IAAI,oCAAoC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;QAC3F,OAAO,IAAI,CAAC;KACb;IAED,MAAM,WAAW,GAAG,0CAA0C,CAAC,UAAU,CAAC,CAC7C;IAC7B,IAAI,WAAY,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,2CAA2C,CAAC,UAAwC;IAC3F,UAAU,CAAC,cAAc,GAAG,SAAU,CAAC;IACvC,UAAU,CAAC,gBAAgB,GAAG,SAAU,CAAC;AAC3C,CAAC;AAED;SAEgB,iCAAiC,CAAC,UAAwC;IACxF,MAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IAExD,IAAI,UAAU,CAAC,eAAe,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAC9D,OAAO;KACR;IAED,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,EAAE;QAClC,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC;QAElC,OAAO;KACR;IAED,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3C,MAAM,oBAAoB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACjE,IAAI,oBAAoB,CAAC,WAAW,GAAG,CAAC,EAAE;YACxC,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;YACnF,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAEjD,MAAM,CAAC,CAAC;SACT;KACF;IAED,2CAA2C,CAAC,UAAU,CAAC,CAAC;IACxD,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAC9B,CAAC;SAEe,mCAAmC,CAAC,UAAwC,EAAE,KAAsB;IAClH,MAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IAExD,IAAI,UAAU,CAAC,eAAe,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAC9D,OAAO;KACR;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5B,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;IACpC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;IAIpC,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAEtD,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3C,MAAM,oBAAoB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACjE,IAAI,gBAAgB,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAIlD;QACD,oBAAoB,CAAC,MAAM,GAAG,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;KAChF;IAED,iDAAiD,CAAC,UAAU,CAAC,CAAC;IAE9D,IAAI,8BAA8B,CAAC,MAAM,CAAC,EAAE;QAC1C,IAAI,gCAAgC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAElD,+CAA+C,CAAC,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;SACxG;aAAM;YAEL,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAE3C,gDAAgD,CAAC,UAAU,CAAC,CAAC;aAC9D;YACD,MAAM,eAAe,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAClF,gCAAgC,CAAC,MAAM,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;SAClE;KACF;SAAM,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE;;QAE9C,+CAA+C,CAAC,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACvG,gEAAgE,CAAC,UAAU,CAAC,CAAC;KAC9E;SAAM;QAEL,+CAA+C,CAAC,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;KACxG;IAED,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAC3D,CAAC;SAEe,iCAAiC,CAAC,UAAwC,EAAE,CAAM;IAChG,MAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IAExD,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO;KACR;IAED,iDAAiD,CAAC,UAAU,CAAC,CAAC;IAE9D,UAAU,CAAC,UAAU,CAAC,CAAC;IACvB,2CAA2C,CAAC,UAAU,CAAC,CAAC;IACxD,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC;SAEe,0CAA0C,CACxD,UAAwC;IAExC,IAAI,UAAU,CAAC,YAAY,KAAK,IAAI,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC/E,MAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAC5D,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,WAAW,EACxD,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;QAEtF,MAAM,WAAW,GAA8B,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;QAClG,8BAA8B,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QAC9D,UAAU,CAAC,YAAY,GAAG,WAAW,CAAC;KACvC;IACD,OAAO,UAAU,CAAC,YAAY,CAAC;AACjC,CAAC;AAED,SAAS,0CAA0C,CAAC,UAAwC;IAC1F,MAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;IAE9D,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IACD,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,OAAO,CAAC,CAAC;KACV;IAED,OAAO,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC;AAC9D,CAAC;SAEe,mCAAmC,CAAC,UAAwC,EAAE,YAAoB;IAGhH,MAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;IAC5D,MAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;IAE9D,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,IAAI,YAAY,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI,SAAS,CAAC,kEAAkE,CAAC,CAAC;SACzF;KACF;SAAM;QAEL,IAAI,YAAY,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI,SAAS,CAAC,iFAAiF,CAAC,CAAC;SACxG;QACD,IAAI,eAAe,CAAC,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC,UAAU,EAAE;YAC3E,MAAM,IAAI,UAAU,CAAC,2BAA2B,CAAC,CAAC;SACnD;KACF;IAED,eAAe,CAAC,MAAM,GAAG,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAErE,2CAA2C,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACxE,CAAC;SAEe,8CAA8C,CAAC,UAAwC,EACxC,IAAqB;IAIlF,MAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;IAC5D,MAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;IAE9D,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YACzB,MAAM,IAAI,SAAS,CAAC,mFAAmF,CAAC,CAAC;SAC1G;KACF;SAAM;QAEL,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YACzB,MAAM,IAAI,SAAS,CACjB,kGAAkG,CACnG,CAAC;SACH;KACF;IAED,IAAI,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,WAAW,KAAK,IAAI,CAAC,UAAU,EAAE;QAChF,MAAM,IAAI,UAAU,CAAC,yDAAyD,CAAC,CAAC;KACjF;IACD,IAAI,eAAe,CAAC,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;QAC/D,MAAM,IAAI,UAAU,CAAC,4DAA4D,CAAC,CAAC;KACpF;IACD,IAAI,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,EAAE;QAC9E,MAAM,IAAI,UAAU,CAAC,yDAAyD,CAAC,CAAC;KACjF;IAED,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC;IACvC,eAAe,CAAC,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1D,2CAA2C,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;AAC1E,CAAC;SAEe,iCAAiC,CAAC,MAA0B,EAC1B,UAAwC,EACxC,cAA8C,EAC9C,aAAkC,EAClC,eAA+C,EAC/C,aAAqB,EACrB,qBAAyC;IAOzF,UAAU,CAAC,6BAA6B,GAAG,MAAM,CAAC;IAElD,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;IAC9B,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;IAE5B,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;;IAG/B,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IAC5D,UAAU,CAAC,UAAU,CAAC,CAAC;IAEvB,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC;IACnC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;IAE5B,UAAU,CAAC,YAAY,GAAG,aAAa,CAAC;IAExC,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC;IAC1C,UAAU,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAE9C,UAAU,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;IAE1D,UAAU,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;IAEjD,MAAM,CAAC,yBAAyB,GAAG,UAAU,CAAC;IAE9C,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IACrC,WAAW,CACT,mBAAmB,CAAC,WAAW,CAAC,EAChC;QACE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAGK;QAE/B,4CAA4C,CAAC,UAAU,CAAC,CAAC;KAC1D,EACD,CAAC;QACC,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;KAClD,CACF,CAAC;AACJ,CAAC;SAEe,qDAAqD,CACnE,MAA0B,EAC1B,oBAAmD,EACnD,aAAqB;IAErB,MAAM,UAAU,GAAiC,MAAM,CAAC,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC;IAEvG,IAAI,cAAc,GAAmC,MAAM,SAAS,CAAC;IACrE,IAAI,aAAa,GAAwB,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAC9E,IAAI,eAAe,GAAmC,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAE3F,IAAI,oBAAoB,CAAC,KAAK,KAAK,SAAS,EAAE;QAC5C,cAAc,GAAG,MAAM,oBAAoB,CAAC,KAAM,CAAC,UAAU,CAAC,CAAC;KAChE;IACD,IAAI,oBAAoB,CAAC,IAAI,KAAK,SAAS,EAAE;QAC3C,aAAa,GAAG,MAAM,oBAAoB,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;KAC9D;IACD,IAAI,oBAAoB,CAAC,MAAM,KAAK,SAAS,EAAE;QAC7C,eAAe,GAAG,MAAM,IAAI,oBAAoB,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC;KAClE;IAED,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,qBAAqB,CAAC;IACzE,IAAI,qBAAqB,KAAK,CAAC,EAAE;QAC/B,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;KACrE;IAED,iCAAiC,CAC/B,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,qBAAqB,CACzG,CAAC;AACJ,CAAC;AAED,SAAS,8BAA8B,CAAC,OAAkC,EAClC,UAAwC,EACxC,IAAqB;IAK3D,OAAO,CAAC,uCAAuC,GAAG,UAAU,CAAC;IAC7D,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACvB,CAAC;AAED;AAEA,SAAS,8BAA8B,CAAC,IAAY;IAClD,OAAO,IAAI,SAAS,CAClB,uCAAuC,IAAI,kDAAkD,CAAC,CAAC;AACnG,CAAC;AAED;AAEA,SAAS,uCAAuC,CAAC,IAAY;IAC3D,OAAO,IAAI,SAAS,CAClB,0CAA0C,IAAI,qDAAqD,CAAC,CAAC;AACzG;;ACv/BA;SAEgB,+BAA+B,CAAC,MAA0B;IACxE,OAAO,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED;SAEgB,gCAAgC,CAA4B,MAA0B,EAC1B,eAAmC;IAI5G,MAAM,CAAC,OAAqC,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACxF,CAAC;SAEe,oCAAoC,CAAC,MAA0B,EAC1B,KAAsB,EACtB,IAAa;IAChE,MAAM,MAAM,GAAG,MAAM,CAAC,OAAmC,CAEb;IAE5C,MAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAG,CAAC;IAC1D,IAAI,IAAI,EAAE;QACR,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACpC;SAAM;QACL,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACpC;AACH,CAAC;SAEe,oCAAoC,CAAC,MAA0B;IAC7E,OAAQ,MAAM,CAAC,OAAoC,CAAC,iBAAiB,CAAC,MAAM,CAAC;AAC/E,CAAC;SAEe,2BAA2B,CAAC,MAA0B;IACpE,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAE9B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,EAAE;QACvC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAYD;;;;;MAKa,wBAAwB;IAYnC,YAAY,MAA0B;QACpC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,0BAA0B,CAAC,CAAC;QAC9D,oBAAoB,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAEhD,IAAI,sBAAsB,CAAC,MAAM,CAAC,EAAE;YAClC,MAAM,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;SACpG;QAED,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE;YACrE,MAAM,IAAI,SAAS,CAAC,uFAAuF;gBACzG,QAAQ,CAAC,CAAC;SACb;QAED,qCAAqC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEpD,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;KAC5C;;;;;IAMD,IAAI,MAAM;QACR,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;YACrC,OAAO,mBAAmB,CAAC,6BAA6B,CAAC,QAAQ,CAAC,CAAC,CAAC;SACrE;QAED,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;;;;IAKD,MAAM,CAAC,SAAc,SAAS;QAC5B,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;YACrC,OAAO,mBAAmB,CAAC,6BAA6B,CAAC,QAAQ,CAAC,CAAC,CAAC;SACrE;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3D;QAED,OAAO,iCAAiC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACxD;;;;;;IAOD,IAAI,CAA4B,IAAO;QACrC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;YACrC,OAAO,mBAAmB,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC,CAAC;SACnE;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC7B,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,CAAC;SAChF;QACD,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YACzB,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC,CAAC;SACjF;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAC,CAAC;SAC1F;QACD,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAElC;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;SAC9D;QAED,IAAI,cAAkE,CAAC;QACvE,IAAI,aAAqC,CAAC;QAC1C,MAAM,OAAO,GAAG,UAAU,CAAkC,CAAC,OAAO,EAAE,MAAM;YAC1E,cAAc,GAAG,OAAO,CAAC;YACzB,aAAa,GAAG,MAAM,CAAC;SACxB,CAAC,CAAC;QACH,MAAM,eAAe,GAAuB;YAC1C,WAAW,EAAE,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YACnE,WAAW,EAAE,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAClE,WAAW,EAAE,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC;SACnC,CAAC;QACF,4BAA4B,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;QAC1D,OAAO,OAAO,CAAC;KAChB;;;;;;;;;;IAWD,WAAW;QACT,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,6BAA6B,CAAC,aAAa,CAAC,CAAC;SACpD;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO;SACR;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,MAAM,IAAI,SAAS,CAAC,qFAAqF,CAAC,CAAC;SAC5G;QAED,kCAAkC,CAAC,IAAI,CAAC,CAAC;KAC1C;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,SAAS,EAAE;IAC1D,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC5B,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC1B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACjC,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC7B,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,wBAAwB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAC5E,KAAK,EAAE,0BAA0B;QACjC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;SAEgB,0BAA0B,CAAC,CAAM;IAC/C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE;QACjE,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,wBAAwB,CAAC;AAC/C,CAAC;SAEe,4BAA4B,CAC1C,MAAgC,EAChC,IAAO,EACP,eAAmC;IAEnC,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;IAE7B,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;IAEzB,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;QAC/B,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAClD;SAAM;QACL,oCAAoC,CAClC,MAAM,CAAC,yBAAyD,EAChE,IAAI,EACJ,eAAe,CAChB,CAAC;KACH;AACH,CAAC;AAED;AAEA,SAAS,6BAA6B,CAAC,IAAY;IACjD,OAAO,IAAI,SAAS,CAClB,sCAAsC,IAAI,iDAAiD,CAAC,CAAC;AACjG;;SCjRgB,oBAAoB,CAAC,QAAyB,EAAE,UAAkB;IAChF,MAAM,EAAE,aAAa,EAAE,GAAG,QAAQ,CAAC;IAEnC,IAAI,aAAa,KAAK,SAAS,EAAE;QAC/B,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,WAAW,CAAC,aAAa,CAAC,IAAI,aAAa,GAAG,CAAC,EAAE;QACnD,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;KAC/C;IAED,OAAO,aAAa,CAAC;AACvB,CAAC;SAEe,oBAAoB,CAAI,QAA4B;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC;IAE1B,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,MAAM,CAAC,CAAC;KAChB;IAED,OAAO,IAAI,CAAC;AACd;;SCtBgB,sBAAsB,CAAI,IAA2C,EAC3C,OAAe;IACvD,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChC,MAAM,aAAa,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;IAC1C,MAAM,IAAI,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC;IACxB,OAAO;QACL,aAAa,EAAE,aAAa,KAAK,SAAS,GAAG,SAAS,GAAG,yBAAyB,CAAC,aAAa,CAAC;QACjG,IAAI,EAAE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,0BAA0B,CAAC,IAAI,EAAE,GAAG,OAAO,yBAAyB,CAAC;KAC7G,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAI,EAAkC,EAClC,OAAe;IACpD,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,KAAK,IAAI,yBAAyB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACvD;;SCNgB,qBAAqB,CAAI,QAAkC,EAClC,OAAe;IACtD,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACpC,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;IAC9B,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;IAC9B,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;IAC9B,MAAM,IAAI,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC;IAC5B,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;IAC9B,OAAO;QACL,KAAK,EAAE,KAAK,KAAK,SAAS;YACxB,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,GAAG,OAAO,0BAA0B,CAAC;QAC5F,KAAK,EAAE,KAAK,KAAK,SAAS;YACxB,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,GAAG,OAAO,0BAA0B,CAAC;QAC5F,KAAK,EAAE,KAAK,KAAK,SAAS;YACxB,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,GAAG,OAAO,0BAA0B,CAAC;QAC5F,KAAK,EAAE,KAAK,KAAK,SAAS;YACxB,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,GAAG,OAAO,0BAA0B,CAAC;QAC5F,IAAI;KACL,CAAC;AACJ,CAAC;AAED,SAAS,kCAAkC,CACzC,EAA+B,EAC/B,QAAwB,EACxB,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,MAAW,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED,SAAS,kCAAkC,CACzC,EAA+B,EAC/B,QAAwB,EACxB,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,MAAM,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,kCAAkC,CACzC,EAA+B,EAC/B,QAAwB,EACxB,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,UAA2C,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AAClG,CAAC;AAED,SAAS,kCAAkC,CACzC,EAAkC,EAClC,QAA2B,EAC3B,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,KAAQ,EAAE,UAA2C,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;AACnH;;SCrEgB,oBAAoB,CAAC,CAAU,EAAE,OAAe;IAC9D,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;QACxB,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,2BAA2B,CAAC,CAAC;KAC5D;AACH;;SCsBgB,aAAa,CAAC,KAAc;IAC1C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;QAC/C,OAAO,KAAK,CAAC;KACd;IACD,IAAI;QACF,OAAO,OAAQ,KAAqB,CAAC,OAAO,KAAK,SAAS,CAAC;KAC5D;IAAC,WAAM;;QAEN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAsBD,MAAM,uBAAuB,GAAG,OAAQ,eAAuB,KAAK,UAAU,CAAC;AAE/E;;;;;SAKgB,qBAAqB;IACnC,IAAI,uBAAuB,EAAE;QAC3B,OAAO,IAAK,eAA8C,EAAE,CAAC;KAC9D;IACD,OAAO,SAAS,CAAC;AACnB;;ACnBA;;;;;AAKA,MAAM,cAAc;IAuBlB,YAAY,oBAA0D,EAAE,EAC5D,cAAqD,EAAE;QACjE,IAAI,iBAAiB,KAAK,SAAS,EAAE;YACnC,iBAAiB,GAAG,IAAI,CAAC;SAC1B;aAAM;YACL,YAAY,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;SACpD;QAED,MAAM,QAAQ,GAAG,sBAAsB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QACzE,MAAM,cAAc,GAAG,qBAAqB,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;QAEnF,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAE/B,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;QACjC,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,MAAM,IAAI,UAAU,CAAC,2BAA2B,CAAC,CAAC;SACnD;QAED,MAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACrD,MAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAExD,sDAAsD,CAAC,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;KAC5G;;;;IAKD,IAAI,MAAM;QACR,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAMC,2BAAyB,CAAC,QAAQ,CAAC,CAAC;SAC3C;QAED,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;KACrC;;;;;;;;;;IAWD,KAAK,CAAC,SAAc,SAAS;QAC3B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;SAChE;QAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC,CAAC;SAC9F;QAED,OAAO,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC1C;;;;;;;;;IAUD,KAAK;QACH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;SAChE;QAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC,CAAC;SAC9F;QAED,IAAI,mCAAmC,CAAC,IAAI,CAAC,EAAE;YAC7C,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC;SACrF;QAED,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;KAClC;;;;;;;;;IAUD,SAAS;QACP,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAMA,2BAAyB,CAAC,WAAW,CAAC,CAAC;SAC9C;QAED,OAAO,kCAAkC,CAAC,IAAI,CAAC,CAAC;KACjD;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE;IAChD,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC/B,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC7B,CAAC,CAAC;AACH,IAAI,OAAOD,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAClE,KAAK,EAAE,gBAAgB;QACvB,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAqBD;AAEA,SAAS,kCAAkC,CAAI,MAAyB;IACtE,OAAO,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC;AAED;AACA,SAAS,oBAAoB,CAAI,cAA8C,EAC9C,cAA2C,EAC3C,cAAmC,EACnC,cAA8C,EAC9C,aAAa,GAAG,CAAC,EACjB,gBAAgD,MAAM,CAAC;IAGtF,MAAM,MAAM,GAAsB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC1E,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAuC,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;IAEhH,oCAAoC,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAClE,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IACnF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,wBAAwB,CAAI,MAAyB;IAC5D,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;;;IAI3B,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;IAEhC,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;;;IAI3B,MAAM,CAAC,yBAAyB,GAAG,SAAU,CAAC;;;IAI9C,MAAM,CAAC,cAAc,GAAG,IAAI,WAAW,EAAE,CAAC;;;IAI1C,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;;;IAIzC,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;;;IAIjC,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;;IAGzC,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;;IAGxC,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,CAAC;AAED,SAAS,gBAAgB,CAAC,CAAU;IAClC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;QACzE,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,cAAc,CAAC;AACrC,CAAC;AAED,SAAS,sBAAsB,CAAC,MAAsB;IAGpD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;QAChC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAsB,EAAE,MAAW;;IAC9D,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;QAC7D,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IACD,MAAM,CAAC,yBAAyB,CAAC,YAAY,GAAG,MAAM,CAAC;IACvD,MAAA,MAAM,CAAC,yBAAyB,CAAC,gBAAgB,0CAAE,KAAK,EAAE,CAAC;;;;IAK3D,MAAM,KAAK,GAAG,MAAM,CAAC,MAA6B,CAAC;IAEnD,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QAC7C,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IACD,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;QAC7C,OAAO,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;KAGO;IAErD,IAAI,kBAAkB,GAAG,KAAK,CAAC;IAC/B,IAAI,KAAK,KAAK,UAAU,EAAE;QACxB,kBAAkB,GAAG,IAAI,CAAC;;QAE1B,MAAM,GAAG,SAAS,CAAC;KACpB;IAED,MAAM,OAAO,GAAG,UAAU,CAAY,CAAC,OAAO,EAAE,MAAM;QACpD,MAAM,CAAC,oBAAoB,GAAG;YAC5B,QAAQ,EAAE,SAAU;YACpB,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,MAAM;YACf,mBAAmB,EAAE,kBAAkB;SACxC,CAAC;KACH,CAAC,CAAC;IACH,MAAM,CAAC,oBAAqB,CAAC,QAAQ,GAAG,OAAO,CAAC;IAEhD,IAAI,CAAC,kBAAkB,EAAE;QACvB,2BAA2B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC7C;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,mBAAmB,CAAC,MAA2B;IACtD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QAC7C,OAAO,mBAAmB,CAAC,IAAI,SAAS,CACtC,kBAAkB,KAAK,2DAA2D,CAAC,CAAC,CAAC;KAIpC;IAErD,MAAM,OAAO,GAAG,UAAU,CAAY,CAAC,OAAO,EAAE,MAAM;QACpD,MAAM,YAAY,GAAiB;YACjC,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,MAAM;SAChB,CAAC;QAEF,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC;KACrC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,aAAa,IAAI,KAAK,KAAK,UAAU,EAAE;QACxE,gCAAgC,CAAC,MAAM,CAAC,CAAC;KAC1C;IAED,oCAAoC,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;IAEvE,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;AAEA,SAAS,6BAA6B,CAAC,MAAsB;IAI3D,MAAM,OAAO,GAAG,UAAU,CAAY,CAAC,OAAO,EAAE,MAAM;QACpD,MAAM,YAAY,GAAiB;YACjC,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,MAAM;SAChB,CAAC;QAEF,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KAC1C,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,+BAA+B,CAAC,MAAsB,EAAE,KAAU;IACzE,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAE5B,IAAI,KAAK,KAAK,UAAU,EAAE;QACxB,2BAA2B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC3C,OAAO;KAGoB;IAC7B,4BAA4B,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,2BAA2B,CAAC,MAAsB,EAAE,MAAW;IAItE,MAAM,UAAU,GAAG,MAAM,CAAC,yBAAyB,CAClB;IAEjC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;IAC3B,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC;IAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,qDAAqD,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACvE;IAED,IAAI,CAAC,wCAAwC,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,EAAE;QAC5E,4BAA4B,CAAC,MAAM,CAAC,CAAC;KACtC;AACH,CAAC;AAED,SAAS,4BAA4B,CAAC,MAAsB;IAG1D,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,EAAE,CAAC;IAE/C,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;IACxC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY;QACxC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;KACnC,CAAC,CAAC;IACH,MAAM,CAAC,cAAc,GAAG,IAAI,WAAW,EAAE,CAAC;IAE1C,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;QAC7C,iDAAiD,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO;KACR;IAED,MAAM,YAAY,GAAG,MAAM,CAAC,oBAAoB,CAAC;IACjD,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;IAExC,IAAI,YAAY,CAAC,mBAAmB,EAAE;QACpC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClC,iDAAiD,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO;KACR;IAED,MAAM,OAAO,GAAG,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACnF,WAAW,CACT,OAAO,EACP;QACE,YAAY,CAAC,QAAQ,EAAE,CAAC;QACxB,iDAAiD,CAAC,MAAM,CAAC,CAAC;KAC3D,EACD,CAAC,MAAW;QACV,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7B,iDAAiD,CAAC,MAAM,CAAC,CAAC;KAC3D,CAAC,CAAC;AACP,CAAC;AAED,SAAS,iCAAiC,CAAC,MAAsB;IAE/D,MAAM,CAAC,qBAAsB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAClD,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC3C,CAAC;AAED,SAAS,0CAA0C,CAAC,MAAsB,EAAE,KAAU;IAEpF,MAAM,CAAC,qBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAE6B;IAErE,+BAA+B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,iCAAiC,CAAC,MAAsB;IAE/D,MAAM,CAAC,qBAAsB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAClD,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;IAEzC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAE0B;IAErD,IAAI,KAAK,KAAK,UAAU,EAAE;;QAExB,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;QAChC,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC7C,MAAM,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;YACvC,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;SACzC;KACF;IAED,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC;IAEzB,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,iCAAiC,CAAC,MAAM,CAAC,CAAC;KAIF;AAC5C,CAAC;AAED,SAAS,0CAA0C,CAAC,MAAsB,EAAE,KAAU;IAEpF,MAAM,CAAC,qBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAE6B;;IAGrE,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;QAC7C,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC3C,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;KACzC;IACD,+BAA+B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC;AAED;AACA,SAAS,mCAAmC,CAAC,MAAsB;IACjE,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QACpF,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,wCAAwC,CAAC,MAAsB;IACtE,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC5F,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sCAAsC,CAAC,MAAsB;IAGpE,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC,aAAa,CAAC;IACpD,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;AACnC,CAAC;AAED,SAAS,2CAA2C,CAAC,MAAsB;IAGzE,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC/D,CAAC;AAED,SAAS,iDAAiD,CAAC,MAAsB;IAE/E,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE;QAGtC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAClD,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;KAClC;IACD,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,gCAAgC,CAAC,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;KAC/D;AACH,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAsB,EAAE,YAAqB;IAIrF,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,IAAI,YAAY,KAAK,MAAM,CAAC,aAAa,EAAE;QACjE,IAAI,YAAY,EAAE;YAChB,8BAA8B,CAAC,MAAM,CAAC,CAAC;SACxC;aAAM;YAGL,gCAAgC,CAAC,MAAM,CAAC,CAAC;SAC1C;KACF;IAED,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC;AACtC,CAAC;AAED;;;;;MAKa,2BAA2B;IAoBtC,YAAY,MAAyB;QACnC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,6BAA6B,CAAC,CAAC;QACjE,oBAAoB,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAEhD,IAAI,sBAAsB,CAAC,MAAM,CAAC,EAAE;YAClC,MAAM,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;SACpG;QAED,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;QACnC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QAEtB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;QAE5B,IAAI,KAAK,KAAK,UAAU,EAAE;YACxB,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,aAAa,EAAE;gBACxE,mCAAmC,CAAC,IAAI,CAAC,CAAC;aAC3C;iBAAM;gBACL,6CAA6C,CAAC,IAAI,CAAC,CAAC;aACrD;YAED,oCAAoC,CAAC,IAAI,CAAC,CAAC;SAC5C;aAAM,IAAI,KAAK,KAAK,UAAU,EAAE;YAC/B,6CAA6C,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;YACzE,oCAAoC,CAAC,IAAI,CAAC,CAAC;SAC5C;aAAM,IAAI,KAAK,KAAK,QAAQ,EAAE;YAC7B,6CAA6C,CAAC,IAAI,CAAC,CAAC;YACpD,8CAA8C,CAAC,IAAI,CAAC,CAAC;SACtD;aAAM;YAGL,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;YACxC,6CAA6C,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACjE,8CAA8C,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SACnE;KACF;;;;;IAMD,IAAI,MAAM;QACR,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;SACxE;QAED,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;;;;;;;;;IAUD,IAAI,WAAW;QACb,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,gCAAgC,CAAC,aAAa,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,MAAM,0BAA0B,CAAC,aAAa,CAAC,CAAC;SACjD;QAED,OAAO,yCAAyC,CAAC,IAAI,CAAC,CAAC;KACxD;;;;;;;;;IAUD,IAAI,KAAK;QACP,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;QAED,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;;;;IAKD,KAAK,CAAC,SAAc,SAAS;QAC3B,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;SACjE;QAED,OAAO,gCAAgC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACvD;;;;IAKD,KAAK;QACH,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEzC,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;SACjE;QAED,IAAI,mCAAmC,CAAC,MAAM,CAAC,EAAE;YAC/C,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC;SACrF;QAED,OAAO,gCAAgC,CAAC,IAAI,CAAC,CAAC;KAC/C;;;;;;;;;;;IAYD,WAAW;QACT,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,gCAAgC,CAAC,aAAa,CAAC,CAAC;SACvD;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEzC,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO;SAG4B;QAErC,kCAAkC,CAAC,IAAI,CAAC,CAAC;KAC1C;IAYD,KAAK,CAAC,QAAW,SAAU;QACzB,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC,CAAC;SACpE;QAED,OAAO,gCAAgC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACtD;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,SAAS,EAAE;IAC7D,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACjC,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC5B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACjC,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC5B,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,2BAA2B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAC/E,KAAK,EAAE,6BAA6B;QACpC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;AAEA,SAAS,6BAA6B,CAAU,CAAM;IACpD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,sBAAsB,CAAC,EAAE;QACpE,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,2BAA2B,CAAC;AAClD,CAAC;AAED;AAEA,SAAS,gCAAgC,CAAC,MAAmC,EAAE,MAAW;IACxF,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;IAE7B,OAAO,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAmC;IAC3E,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;IAE7B,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,oDAAoD,CAAC,MAAmC;IAC/F,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;IAE7B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,IAAI,mCAAmC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,QAAQ,EAAE;QACrE,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAGG;IAErD,OAAO,gCAAgC,CAAC,MAAM,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,sDAAsD,CAAC,MAAmC,EAAE,KAAU;IAC7G,IAAI,MAAM,CAAC,mBAAmB,KAAK,SAAS,EAAE;QAC5C,gCAAgC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACjD;SAAM;QACL,yCAAyC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC1D;AACH,CAAC;AAED,SAAS,qDAAqD,CAAC,MAAmC,EAAE,KAAU;IAC5G,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE;QAC3C,+BAA+B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAChD;SAAM;QACL,wCAAwC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACzD;AACH,CAAC;AAED,SAAS,yCAAyC,CAAC,MAAmC;IACpF,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAAC;IAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAE5B,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,UAAU,EAAE;QAC/C,OAAO,IAAI,CAAC;KACb;IAED,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,OAAO,CAAC,CAAC;KACV;IAED,OAAO,6CAA6C,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;AACzF,CAAC;AAED,SAAS,kCAAkC,CAAC,MAAmC;IAC7E,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAER;IAElC,MAAM,aAAa,GAAG,IAAI,SAAS,CACjC,kFAAkF,CAAC,CAAC;IAEtF,qDAAqD,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;;;IAI7E,sDAAsD,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAE9E,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;IAC3B,MAAM,CAAC,oBAAoB,GAAG,SAAU,CAAC;AAC3C,CAAC;AAED,SAAS,gCAAgC,CAAI,MAAsC,EAAE,KAAQ;IAC3F,MAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;IAE7B,MAAM,UAAU,GAAG,MAAM,CAAC,yBAAyB,CAAC;IAEpD,MAAM,SAAS,GAAG,2CAA2C,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IAEjF,IAAI,MAAM,KAAK,MAAM,CAAC,oBAAoB,EAAE;QAC1C,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC,CAAC;KACpE;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KACjD;IACD,IAAI,mCAAmC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,QAAQ,EAAE;QACrE,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,0DAA0D,CAAC,CAAC,CAAC;KACvG;IACD,IAAI,KAAK,KAAK,UAAU,EAAE;QACxB,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAGrB;IAE7B,MAAM,OAAO,GAAG,6BAA6B,CAAC,MAAM,CAAC,CAAC;IAEtD,oCAAoC,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAEnE,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,aAAa,GAAkB,EAAS,CAAC;AAI/C;;;;;MAKa,+BAA+B;IAwB1C;QACE,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;;;;;;;;IASD,IAAI,WAAW;QACb,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAME,sCAAoC,CAAC,aAAa,CAAC,CAAC;SAC3D;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;;;;IAKD,IAAI,MAAM;QACR,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAMA,sCAAoC,CAAC,QAAQ,CAAC,CAAC;SACtD;QACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;;;;YAIvC,MAAM,IAAI,SAAS,CAAC,mEAAmE,CAAC,CAAC;SAC1F;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;KACrC;;;;;;;;IASD,KAAK,CAAC,IAAS,SAAS;QACtB,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAMA,sCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;QACpD,IAAI,KAAK,KAAK,UAAU,EAAE;;;YAGxB,OAAO;SACR;QAED,oCAAoC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC/C;;IAGD,CAAC,UAAU,CAAC,CAAC,MAAW;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC5C,8CAA8C,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,MAAM,CAAC;KACf;;IAGD,CAAC,UAAU,CAAC;QACV,UAAU,CAAC,IAAI,CAAC,CAAC;KAClB;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,SAAS,EAAE;IACjE,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACjC,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC5B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC5B,CAAC,CAAC;AACH,IAAI,OAAOF,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,+BAA+B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QACnF,KAAK,EAAE,iCAAiC;QACxC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;AAEA,SAAS,iCAAiC,CAAC,CAAM;IAC/C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;QACzE,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,+BAA+B,CAAC;AACtD,CAAC;AAED,SAAS,oCAAoC,CAAI,MAAyB,EACzB,UAA8C,EAC9C,cAA8C,EAC9C,cAA2C,EAC3C,cAAmC,EACnC,cAA8C,EAC9C,aAAqB,EACrB,aAA6C;IAI5F,UAAU,CAAC,yBAAyB,GAAG,MAAM,CAAC;IAC9C,MAAM,CAAC,yBAAyB,GAAG,UAAU,CAAC;;IAG9C,UAAU,CAAC,MAAM,GAAG,SAAU,CAAC;IAC/B,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IACxC,UAAU,CAAC,UAAU,CAAC,CAAC;IAEvB,UAAU,CAAC,YAAY,GAAG,SAAS,CAAC;IACpC,UAAU,CAAC,gBAAgB,GAAG,qBAAqB,EAAE,CAAC;IACtD,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;IAE5B,UAAU,CAAC,sBAAsB,GAAG,aAAa,CAAC;IAClD,UAAU,CAAC,YAAY,GAAG,aAAa,CAAC;IAExC,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;IAC5C,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;IAC5C,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;IAE5C,MAAM,YAAY,GAAG,8CAA8C,CAAC,UAAU,CAAC,CAAC;IAChF,gCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IAEvD,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IACrC,MAAM,YAAY,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACtD,WAAW,CACT,YAAY,EACZ;QAEE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC3B,mDAAmD,CAAC,UAAU,CAAC,CAAC;KACjE,EACD,CAAC;QAEC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC3B,+BAA+B,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KAC5C,CACF,CAAC;AACJ,CAAC;AAED,SAAS,sDAAsD,CAAI,MAAyB,EACzB,cAA0C,EAC1C,aAAqB,EACrB,aAA6C;IAC9G,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;IAE5E,IAAI,cAAc,GAAmC,MAAM,SAAS,CAAC;IACrE,IAAI,cAAc,GAAgC,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACvF,IAAI,cAAc,GAAwB,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAC/E,IAAI,cAAc,GAAmC,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAE1F,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;QACtC,cAAc,GAAG,MAAM,cAAc,CAAC,KAAM,CAAC,UAAU,CAAC,CAAC;KAC1D;IACD,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;QACtC,cAAc,GAAG,KAAK,IAAI,cAAc,CAAC,KAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;KACpE;IACD,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;QACtC,cAAc,GAAG,MAAM,cAAc,CAAC,KAAM,EAAE,CAAC;KAChD;IACD,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;QACtC,cAAc,GAAG,MAAM,IAAI,cAAc,CAAC,KAAM,CAAC,MAAM,CAAC,CAAC;KAC1D;IAED,oCAAoC,CAClC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,CACjH,CAAC;AACJ,CAAC;AAED;AACA,SAAS,8CAA8C,CAAC,UAAgD;IACtG,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IACxC,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IACxC,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IACxC,UAAU,CAAC,sBAAsB,GAAG,SAAU,CAAC;AACjD,CAAC;AAED,SAAS,oCAAoC,CAAI,UAA8C;IAC7F,oBAAoB,CAAC,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;IACnD,mDAAmD,CAAC,UAAU,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,2CAA2C,CAAI,UAA8C,EAC9C,KAAQ;IAC9D,IAAI;QACF,OAAO,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;KACjD;IAAC,OAAO,UAAU,EAAE;QACnB,4CAA4C,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACrE,OAAO,CAAC,CAAC;KACV;AACH,CAAC;AAED,SAAS,6CAA6C,CAAC,UAAgD;IACrG,OAAO,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC;AAC9D,CAAC;AAED,SAAS,oCAAoC,CAAI,UAA8C,EAC9C,KAAQ,EACR,SAAiB;IAChE,IAAI;QACF,oBAAoB,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;KACpD;IAAC,OAAO,QAAQ,EAAE;QACjB,4CAA4C,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACnE,OAAO;KACR;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IACpD,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChF,MAAM,YAAY,GAAG,8CAA8C,CAAC,UAAU,CAAC,CAAC;QAChF,gCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;KACxD;IAED,mDAAmD,CAAC,UAAU,CAAC,CAAC;AAClE,CAAC;AAED;AAEA,SAAS,mDAAmD,CAAI,UAA8C;IAC5G,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;QACxB,OAAO;KACR;IAED,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KACR;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACuB;IAClD,IAAI,KAAK,KAAK,UAAU,EAAE;QACxB,4BAA4B,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO;KACR;IAED,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO;KACR;IAED,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;IACzC,IAAI,KAAK,KAAK,aAAa,EAAE;QAC3B,2CAA2C,CAAC,UAAU,CAAC,CAAC;KACzD;SAAM;QACL,2CAA2C,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KAChE;AACH,CAAC;AAED,SAAS,4CAA4C,CAAC,UAAgD,EAAE,KAAU;IAChH,IAAI,UAAU,CAAC,yBAAyB,CAAC,MAAM,KAAK,UAAU,EAAE;QAC9D,oCAAoC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KACzD;AACH,CAAC;AAED,SAAS,2CAA2C,CAAC,UAAgD;IACnG,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,sCAAsC,CAAC,MAAM,CAAC,CAAC;IAE/C,YAAY,CAAC,UAAU,CAAC,CACe;IAEvC,MAAM,gBAAgB,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;IACtD,8CAA8C,CAAC,UAAU,CAAC,CAAC;IAC3D,WAAW,CACT,gBAAgB,EAChB;QACE,iCAAiC,CAAC,MAAM,CAAC,CAAC;KAC3C,EACD,MAAM;QACJ,0CAA0C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC5D,CACF,CAAC;AACJ,CAAC;AAED,SAAS,2CAA2C,CAAI,UAA8C,EAAE,KAAQ;IAC9G,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,2CAA2C,CAAC,MAAM,CAAC,CAAC;IAEpD,MAAM,gBAAgB,GAAG,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC3D,WAAW,CACT,gBAAgB,EAChB;QACE,iCAAiC,CAAC,MAAM,CAAC,CAAC;QAE1C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAC0B;QAErD,YAAY,CAAC,UAAU,CAAC,CAAC;QAEzB,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,UAAU,EAAE;YACxE,MAAM,YAAY,GAAG,8CAA8C,CAAC,UAAU,CAAC,CAAC;YAChF,gCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;SACxD;QAED,mDAAmD,CAAC,UAAU,CAAC,CAAC;KACjE,EACD,MAAM;QACJ,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;YAChC,8CAA8C,CAAC,UAAU,CAAC,CAAC;SAC5D;QACD,0CAA0C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC5D,CACF,CAAC;AACJ,CAAC;AAED,SAAS,8CAA8C,CAAC,UAAgD;IACtG,MAAM,WAAW,GAAG,6CAA6C,CAAC,UAAU,CAAC,CAAC;IAC9E,OAAO,WAAW,IAAI,CAAC,CAAC;AAC1B,CAAC;AAED;AAEA,SAAS,oCAAoC,CAAC,UAAgD,EAAE,KAAU;IACxG,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAEd;IAErC,8CAA8C,CAAC,UAAU,CAAC,CAAC;IAC3D,2BAA2B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC;AAED;AAEA,SAASC,2BAAyB,CAAC,IAAY;IAC7C,OAAO,IAAI,SAAS,CAAC,4BAA4B,IAAI,uCAAuC,CAAC,CAAC;AAChG,CAAC;AAED;AAEA,SAASC,sCAAoC,CAAC,IAAY;IACxD,OAAO,IAAI,SAAS,CAClB,6CAA6C,IAAI,wDAAwD,CAAC,CAAC;AAC/G,CAAC;AAGD;AAEA,SAAS,gCAAgC,CAAC,IAAY;IACpD,OAAO,IAAI,SAAS,CAClB,yCAAyC,IAAI,oDAAoD,CAAC,CAAC;AACvG,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAY;IAC9C,OAAO,IAAI,SAAS,CAAC,SAAS,GAAG,IAAI,GAAG,mCAAmC,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,oCAAoC,CAAC,MAAmC;IAC/E,MAAM,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,OAAO,EAAE,MAAM;QACjD,MAAM,CAAC,sBAAsB,GAAG,OAAO,CAAC;QACxC,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC;QACtC,MAAM,CAAC,mBAAmB,GAAG,SAAS,CAAC;KACxC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,8CAA8C,CAAC,MAAmC,EAAE,MAAW;IACtG,oCAAoC,CAAC,MAAM,CAAC,CAAC;IAC7C,gCAAgC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,8CAA8C,CAAC,MAAmC;IACzF,oCAAoC,CAAC,MAAM,CAAC,CAAC;IAC7C,iCAAiC,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAmC,EAAE,MAAW;IACxF,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KAEwC;IAEjD,yBAAyB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACjD,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;IAC1C,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;IACzC,MAAM,CAAC,mBAAmB,GAAG,UAAU,CAAC;AAC1C,CAAC;AAED,SAAS,yCAAyC,CAAC,MAAmC,EAAE,MAAW;IAKjG,8CAA8C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,iCAAiC,CAAC,MAAmC;IAC5E,IAAI,MAAM,CAAC,sBAAsB,KAAK,SAAS,EAAE;QAC/C,OAAO;KAEwC;IAEjD,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;IACzC,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;IAC1C,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;IACzC,MAAM,CAAC,mBAAmB,GAAG,UAAU,CAAC;AAC1C,CAAC;AAED,SAAS,mCAAmC,CAAC,MAAmC;IAC9E,MAAM,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC,OAAO,EAAE,MAAM;QAChD,MAAM,CAAC,qBAAqB,GAAG,OAAO,CAAC;QACvC,MAAM,CAAC,oBAAoB,GAAG,MAAM,CAAC;KACtC,CAAC,CAAC;IACH,MAAM,CAAC,kBAAkB,GAAG,SAAS,CAAC;AACxC,CAAC;AAED,SAAS,6CAA6C,CAAC,MAAmC,EAAE,MAAW;IACrG,mCAAmC,CAAC,MAAM,CAAC,CAAC;IAC5C,+BAA+B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,6CAA6C,CAAC,MAAmC;IACxF,mCAAmC,CAAC,MAAM,CAAC,CAAC;IAC5C,gCAAgC,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,+BAA+B,CAAC,MAAmC,EAAE,MAAW;IACvF,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;QAC7C,OAAO;KACR;IAED,yBAAyB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAChD,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IACpC,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;IACzC,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;IACxC,MAAM,CAAC,kBAAkB,GAAG,UAAU,CAAC;AACzC,CAAC;AAED,SAAS,8BAA8B,CAAC,MAAmC;IAIzE,mCAAmC,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,wCAAwC,CAAC,MAAmC,EAAE,MAAW;IAIhG,6CAA6C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAChE,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAmC;IAC3E,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KACR;IAED,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;IACxC,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;IACzC,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;IACxC,MAAM,CAAC,kBAAkB,GAAG,WAAW,CAAC;AAC1C;;ACj4CA;AACO,MAAM,kBAAkB,GAC7B,OAAO,YAAY,KAAK,WAAW,GAAG,YAAY,GAAG,SAAS;;ACFhE;AAaA,SAAS,yBAAyB,CAAC,IAAa;IAC9C,IAAI,EAAE,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE;QAC7D,OAAO,KAAK,CAAC;KACd;IACD,IAAI;QACF,IAAK,IAAgC,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC;KACb;IAAC,WAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED,SAAS,0BAA0B;;IAEjC,MAAM,IAAI,GAAG,SAAS,YAAY,CAAqB,OAAgB,EAAE,IAAa;QACpF,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,OAAO,CAAC;QAC5B,IAAI,KAAK,CAAC,iBAAiB,EAAE;YAC3B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SACjD;KACK,CAAC;IACT,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1G,OAAO,IAAI,CAAC;AACd,CAAC;AAED;AACA,MAAMC,cAAY,GAChB,yBAAyB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB,GAAG,0BAA0B,EAAE;;SCbnF,oBAAoB,CAAI,MAAyB,EACzB,IAAuB,EACvB,YAAqB,EACrB,YAAqB,EACrB,aAAsB,EACtB,MAA+B;IAUrE,MAAM,MAAM,GAAG,kCAAkC,CAAI,MAAM,CAAC,CAAC;IAC7D,MAAM,MAAM,GAAG,kCAAkC,CAAI,IAAI,CAAC,CAAC;IAE3D,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;IAEzB,IAAI,YAAY,GAAG,KAAK,CAAC;;IAGzB,IAAI,YAAY,GAAG,mBAAmB,CAAO,SAAS,CAAC,CAAC;IAExD,OAAO,UAAU,CAAC,CAAC,OAAO,EAAE,MAAM;QAChC,IAAI,cAA0B,CAAC;QAC/B,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,cAAc,GAAG;gBACf,MAAM,KAAK,GAAG,IAAIA,cAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBACxD,MAAM,OAAO,GAA+B,EAAE,CAAC;gBAC/C,IAAI,CAAC,YAAY,EAAE;oBACjB,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;4BAC9B,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;yBACzC;wBACD,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;qBACvC,CAAC,CAAC;iBACJ;gBACD,IAAI,CAAC,aAAa,EAAE;oBAClB,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;4BAChC,OAAO,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;yBAC5C;wBACD,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;qBACvC,CAAC,CAAC;iBACJ;gBACD,kBAAkB,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aACrF,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE;gBAClB,cAAc,EAAE,CAAC;gBACjB,OAAO;aACR;YAED,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;SAClD;;;;QAKD,SAAS,QAAQ;YACf,OAAO,UAAU,CAAO,CAAC,WAAW,EAAE,UAAU;gBAC9C,SAAS,IAAI,CAAC,IAAa;oBACzB,IAAI,IAAI,EAAE;wBACR,WAAW,EAAE,CAAC;qBACf;yBAAM;;;wBAGL,kBAAkB,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;qBAClD;iBACF;gBAED,IAAI,CAAC,KAAK,CAAC,CAAC;aACb,CAAC,CAAC;SACJ;QAED,SAAS,QAAQ;YACf,IAAI,YAAY,EAAE;gBAChB,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;aAClC;YAED,OAAO,kBAAkB,CAAC,MAAM,CAAC,aAAa,EAAE;gBAC9C,OAAO,UAAU,CAAU,CAAC,WAAW,EAAE,UAAU;oBACjD,+BAA+B,CAC7B,MAAM,EACN;wBACE,WAAW,EAAE,KAAK;4BAChB,YAAY,GAAG,kBAAkB,CAAC,gCAAgC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;4BACpG,WAAW,CAAC,KAAK,CAAC,CAAC;yBACpB;wBACD,WAAW,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;wBACpC,WAAW,EAAE,UAAU;qBACxB,CACF,CAAC;iBACH,CAAC,CAAC;aACJ,CAAC,CAAC;SACJ;;QAGD,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,EAAE,WAAW;YAC3D,IAAI,CAAC,YAAY,EAAE;gBACjB,kBAAkB,CAAC,MAAM,mBAAmB,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;aACrF;iBAAM;gBACL,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aAC7B;SACF,CAAC,CAAC;;QAGH,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,cAAc,EAAE,WAAW;YACzD,IAAI,CAAC,aAAa,EAAE;gBAClB,kBAAkB,CAAC,MAAM,oBAAoB,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;aACxF;iBAAM;gBACL,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aAC7B;SACF,CAAC,CAAC;;QAGH,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,EAAE;YAC/C,IAAI,CAAC,YAAY,EAAE;gBACjB,kBAAkB,CAAC,MAAM,oDAAoD,CAAC,MAAM,CAAC,CAAC,CAAC;aACxF;iBAAM;gBACL,QAAQ,EAAE,CAAC;aACZ;SACF,CAAC,CAAC;;QAGH,IAAI,mCAAmC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;YACzE,MAAM,UAAU,GAAG,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;YAEhH,IAAI,CAAC,aAAa,EAAE;gBAClB,kBAAkB,CAAC,MAAM,oBAAoB,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;aACtF;iBAAM;gBACL,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;aAC5B;SACF;QAED,yBAAyB,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEtC,SAAS,qBAAqB;;;YAG5B,MAAM,eAAe,GAAG,YAAY,CAAC;YACrC,OAAO,kBAAkB,CACvB,YAAY,EACZ,MAAM,eAAe,KAAK,YAAY,GAAG,qBAAqB,EAAE,GAAG,SAAS,CAC7E,CAAC;SACH;QAED,SAAS,kBAAkB,CAAC,MAAuC,EACvC,OAAsB,EACtB,MAA6B;YACvD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;gBAC/B,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aAC7B;iBAAM;gBACL,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAChC;SACF;QAED,SAAS,iBAAiB,CAAC,MAAuC,EAAE,OAAsB,EAAE,MAAkB;YAC5G,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;gBAC9B,MAAM,EAAE,CAAC;aACV;iBAAM;gBACL,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAClC;SACF;QAED,SAAS,kBAAkB,CAAC,MAA8B,EAAE,eAAyB,EAAE,aAAmB;YACxG,IAAI,YAAY,EAAE;gBAChB,OAAO;aACR;YACD,YAAY,GAAG,IAAI,CAAC;YAEpB,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,EAAE;gBAC5E,eAAe,CAAC,qBAAqB,EAAE,EAAE,SAAS,CAAC,CAAC;aACrD;iBAAM;gBACL,SAAS,EAAE,CAAC;aACb;YAED,SAAS,SAAS;gBAChB,WAAW,CACT,MAAM,EAAE,EACR,MAAM,QAAQ,CAAC,eAAe,EAAE,aAAa,CAAC,EAC9C,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CACrC,CAAC;aACH;SACF;QAED,SAAS,QAAQ,CAAC,OAAiB,EAAE,KAAW;YAC9C,IAAI,YAAY,EAAE;gBAChB,OAAO;aACR;YACD,YAAY,GAAG,IAAI,CAAC;YAEpB,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,EAAE;gBAC5E,eAAe,CAAC,qBAAqB,EAAE,EAAE,MAAM,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;aAC1E;iBAAM;gBACL,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aAC1B;SACF;QAED,SAAS,QAAQ,CAAC,OAAiB,EAAE,KAAW;YAC9C,kCAAkC,CAAC,MAAM,CAAC,CAAC;YAC3C,kCAAkC,CAAC,MAAM,CAAC,CAAC;YAE3C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;aACrD;YACD,IAAI,OAAO,EAAE;gBACX,MAAM,CAAC,KAAK,CAAC,CAAC;aACf;iBAAM;gBACL,OAAO,CAAC,SAAS,CAAC,CAAC;aACpB;SACF;KACF,CAAC,CAAC;AACL;;ACnOA;;;;;MAKa,+BAA+B;IAwB1C;QACE,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;;;;;IAMD,IAAI,WAAW;QACb,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAMD,sCAAoC,CAAC,aAAa,CAAC,CAAC;SAC3D;QAED,OAAO,6CAA6C,CAAC,IAAI,CAAC,CAAC;KAC5D;;;;;IAMD,KAAK;QACH,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAMA,sCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;QAED,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,EAAE;YAC3D,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC;SACxE;QAED,oCAAoC,CAAC,IAAI,CAAC,CAAC;KAC5C;IAMD,OAAO,CAAC,QAAW,SAAU;QAC3B,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAMA,sCAAoC,CAAC,SAAS,CAAC,CAAC;SACvD;QAED,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,EAAE;YAC3D,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;QAED,OAAO,sCAAsC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC5D;;;;IAKD,KAAK,CAAC,IAAS,SAAS;QACtB,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAMA,sCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;QAED,oCAAoC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC/C;;IAGD,CAAC,WAAW,CAAC,CAAC,MAAW;QACvB,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7C,8CAA8C,CAAC,IAAI,CAAC,CAAC;QACrD,OAAO,MAAM,CAAC;KACf;;IAGD,CAAC,SAAS,CAAC,CAAC,WAA2B;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAE9C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;YAEjC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpD,8CAA8C,CAAC,IAAI,CAAC,CAAC;gBACrD,mBAAmB,CAAC,MAAM,CAAC,CAAC;aAC7B;iBAAM;gBACL,+CAA+C,CAAC,IAAI,CAAC,CAAC;aACvD;YAED,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAChC;aAAM;YACL,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAClD,+CAA+C,CAAC,IAAI,CAAC,CAAC;SACvD;KACF;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,SAAS,EAAE;IACjE,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC7B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAClC,CAAC,CAAC;AACH,IAAI,OAAOF,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,+BAA+B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QACnF,KAAK,EAAE,iCAAiC;QACxC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;AAEA,SAAS,iCAAiC,CAAU,CAAM;IACxD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;QACzE,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,+BAA+B,CAAC;AACtD,CAAC;AAED,SAAS,+CAA+C,CAAC,UAAgD;IACvG,MAAM,UAAU,GAAG,6CAA6C,CAAC,UAAU,CAAC,CAAC;IAC7E,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;IAED,IAAI,UAAU,CAAC,QAAQ,EAAE;QACvB,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,OAAO;KAGsB;IAE/B,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;IAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;IAChD,WAAW,CACT,WAAW,EACX;QACE,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;QAE5B,IAAI,UAAU,CAAC,UAAU,EAAE;YACzB,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;YAC9B,+CAA+C,CAAC,UAAU,CAAC,CAAC;SAC7D;KACF,EACD,CAAC;QACC,oCAAoC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;KACrD,CACF,CAAC;AACJ,CAAC;AAED,SAAS,6CAA6C,CAAC,UAAgD;IACrG,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,IAAI,CAAC,gDAAgD,CAAC,UAAU,CAAC,EAAE;QACjE,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,sBAAsB,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;QAClF,OAAO,IAAI,CAAC;KACb;IAED,MAAM,WAAW,GAAG,6CAA6C,CAAC,UAAU,CAAC,CAChD;IAC7B,IAAI,WAAY,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,8CAA8C,CAAC,UAAgD;IACtG,UAAU,CAAC,cAAc,GAAG,SAAU,CAAC;IACvC,UAAU,CAAC,gBAAgB,GAAG,SAAU,CAAC;IACzC,UAAU,CAAC,sBAAsB,GAAG,SAAU,CAAC;AACjD,CAAC;AAED;SAEgB,oCAAoC,CAAC,UAAgD;IACnG,IAAI,CAAC,gDAAgD,CAAC,UAAU,CAAC,EAAE;QACjE,OAAO;KACR;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC;IAElC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,8CAA8C,CAAC,UAAU,CAAC,CAAC;QAC3D,mBAAmB,CAAC,MAAM,CAAC,CAAC;KAC7B;AACH,CAAC;SAEe,sCAAsC,CACpD,UAA8C,EAC9C,KAAQ;IAER,IAAI,CAAC,gDAAgD,CAAC,UAAU,CAAC,EAAE;QACjE,OAAO;KACR;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,IAAI,sBAAsB,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;QAClF,gCAAgC,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KACxD;SAAM;QACL,IAAI,SAAS,CAAC;QACd,IAAI;YACF,SAAS,GAAG,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;SACtD;QAAC,OAAO,UAAU,EAAE;YACnB,oCAAoC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAC7D,MAAM,UAAU,CAAC;SAClB;QAED,IAAI;YACF,oBAAoB,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SACpD;QAAC,OAAO,QAAQ,EAAE;YACjB,oCAAoC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC3D,MAAM,QAAQ,CAAC;SAChB;KACF;IAED,+CAA+C,CAAC,UAAU,CAAC,CAAC;AAC9D,CAAC;SAEe,oCAAoC,CAAC,UAAgD,EAAE,CAAM;IAC3G,MAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO;KACR;IAED,UAAU,CAAC,UAAU,CAAC,CAAC;IAEvB,8CAA8C,CAAC,UAAU,CAAC,CAAC;IAC3D,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC;SAEe,6CAA6C,CAC3D,UAAgD;IAEhD,MAAM,KAAK,GAAG,UAAU,CAAC,yBAAyB,CAAC,MAAM,CAAC;IAE1D,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IACD,IAAI,KAAK,KAAK,QAAQ,EAAE;QACtB,OAAO,CAAC,CAAC;KACV;IAED,OAAO,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC;AAC9D,CAAC;AAED;SACgB,8CAA8C,CAC5D,UAAgD;IAEhD,IAAI,6CAA6C,CAAC,UAAU,CAAC,EAAE;QAC7D,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;SAEe,gDAAgD,CAC9D,UAAgD;IAEhD,MAAM,KAAK,GAAG,UAAU,CAAC,yBAAyB,CAAC,MAAM,CAAC;IAE1D,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,KAAK,KAAK,UAAU,EAAE;QACvD,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC;SAEe,oCAAoC,CAAI,MAAyB,EACzB,UAA8C,EAC9C,cAA8C,EAC9C,aAAkC,EAClC,eAA+C,EAC/C,aAAqB,EACrB,aAA6C;IAGnG,UAAU,CAAC,yBAAyB,GAAG,MAAM,CAAC;IAE9C,UAAU,CAAC,MAAM,GAAG,SAAU,CAAC;IAC/B,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IACxC,UAAU,CAAC,UAAU,CAAC,CAAC;IAEvB,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC5B,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC;IACnC,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;IAC9B,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;IAE5B,UAAU,CAAC,sBAAsB,GAAG,aAAa,CAAC;IAClD,UAAU,CAAC,YAAY,GAAG,aAAa,CAAC;IAExC,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC;IAC1C,UAAU,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAE9C,MAAM,CAAC,yBAAyB,GAAG,UAAU,CAAC;IAE9C,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;IACrC,WAAW,CACT,mBAAmB,CAAC,WAAW,CAAC,EAChC;QACE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAGK;QAE/B,+CAA+C,CAAC,UAAU,CAAC,CAAC;KAC7D,EACD,CAAC;QACC,oCAAoC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;KACrD,CACF,CAAC;AACJ,CAAC;SAEe,wDAAwD,CACtE,MAAyB,EACzB,gBAA8C,EAC9C,aAAqB,EACrB,aAA6C;IAE7C,MAAM,UAAU,GAAuC,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;IAEhH,IAAI,cAAc,GAAmC,MAAM,SAAS,CAAC;IACrE,IAAI,aAAa,GAAwB,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAC9E,IAAI,eAAe,GAAmC,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAE3F,IAAI,gBAAgB,CAAC,KAAK,KAAK,SAAS,EAAE;QACxC,cAAc,GAAG,MAAM,gBAAgB,CAAC,KAAM,CAAC,UAAU,CAAC,CAAC;KAC5D;IACD,IAAI,gBAAgB,CAAC,IAAI,KAAK,SAAS,EAAE;QACvC,aAAa,GAAG,MAAM,gBAAgB,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;KAC1D;IACD,IAAI,gBAAgB,CAAC,MAAM,KAAK,SAAS,EAAE;QACzC,eAAe,GAAG,MAAM,IAAI,gBAAgB,CAAC,MAAO,CAAC,MAAM,CAAC,CAAC;KAC9D;IAED,oCAAoC,CAClC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,CACjG,CAAC;AACJ,CAAC;AAED;AAEA,SAASE,sCAAoC,CAAC,IAAY;IACxD,OAAO,IAAI,SAAS,CAClB,6CAA6C,IAAI,wDAAwD,CAAC,CAAC;AAC/G;;SCtWgB,iBAAiB,CAAI,MAAyB,EACzB,eAAwB;IAG3D,IAAI,8BAA8B,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE;QACpE,OAAO,qBAAqB,CAAC,MAAuC,CACjB,CAAC;KACrD;IACD,OAAO,wBAAwB,CAAC,MAAuB,CAAC,CAAC;AAC3D,CAAC;SAEe,wBAAwB,CAAI,MAAyB,EACzB,eAAwB;IAIlE,MAAM,MAAM,GAAG,kCAAkC,CAAI,MAAM,CAAC,CAAC;IAE7D,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,OAAY,CAAC;IACjB,IAAI,OAAY,CAAC;IACjB,IAAI,OAA0B,CAAC;IAC/B,IAAI,OAA0B,CAAC;IAE/B,IAAI,oBAAqE,CAAC;IAC1E,MAAM,aAAa,GAAG,UAAU,CAAY,OAAO;QACjD,oBAAoB,GAAG,OAAO,CAAC;KAChC,CAAC,CAAC;IAEH,SAAS,aAAa;QACpB,IAAI,OAAO,EAAE;YACX,SAAS,GAAG,IAAI,CAAC;YACjB,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,OAAO,GAAG,IAAI,CAAC;QAEf,MAAM,WAAW,GAAmB;YAClC,WAAW,EAAE,KAAK;;;;gBAIhB,cAAc,CAAC;oBACb,SAAS,GAAG,KAAK,CAAC;oBAClB,MAAM,MAAM,GAAG,KAAK,CAAC;oBACrB,MAAM,MAAM,GAAG,KAAK,CAAC;;;;;;oBAQrB,IAAI,CAAC,SAAS,EAAE;wBACd,sCAAsC,CACpC,OAAO,CAAC,yBAA+D,EACvE,MAAM,CACP,CAAC;qBACH;oBACD,IAAI,CAAC,SAAS,EAAE;wBACd,sCAAsC,CACpC,OAAO,CAAC,yBAA+D,EACvE,MAAM,CACP,CAAC;qBACH;oBAED,OAAO,GAAG,KAAK,CAAC;oBAChB,IAAI,SAAS,EAAE;wBACb,aAAa,EAAE,CAAC;qBACjB;iBACF,CAAC,CAAC;aACJ;YACD,WAAW,EAAE;gBACX,OAAO,GAAG,KAAK,CAAC;gBAChB,IAAI,CAAC,SAAS,EAAE;oBACd,oCAAoC,CAAC,OAAO,CAAC,yBAA+D,CAAC,CAAC;iBAC/G;gBACD,IAAI,CAAC,SAAS,EAAE;oBACd,oCAAoC,CAAC,OAAO,CAAC,yBAA+D,CAAC,CAAC;iBAC/G;gBAED,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;oBAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBACjC;aACF;YACD,WAAW,EAAE;gBACX,OAAO,GAAG,KAAK,CAAC;aACjB;SACF,CAAC;QACF,+BAA+B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAErD,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,SAAS,gBAAgB,CAAC,MAAW;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,MAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,MAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;QACD,OAAO,aAAa,CAAC;KACtB;IAED,SAAS,gBAAgB,CAAC,MAAW;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,MAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,MAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;QACD,OAAO,aAAa,CAAC;KACtB;IAED,SAAS,cAAc;;KAEtB;IAED,OAAO,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAChF,OAAO,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAEhF,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAM;QAC1C,oCAAoC,CAAC,OAAO,CAAC,yBAA+D,EAAE,CAAC,CAAC,CAAC;QACjH,oCAAoC,CAAC,OAAO,CAAC,yBAA+D,EAAE,CAAC,CAAC,CAAC;QACjH,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;YAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;SACjC;KACF,CAAC,CAAC;IAEH,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5B,CAAC;SAEe,qBAAqB,CAAC,MAA0B;IAI9D,IAAI,MAAM,GAAqC,kCAAkC,CAAC,MAAM,CAAC,CAAC;IAC1F,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAChC,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAChC,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,OAAY,CAAC;IACjB,IAAI,OAAY,CAAC;IACjB,IAAI,OAA2B,CAAC;IAChC,IAAI,OAA2B,CAAC;IAEhC,IAAI,oBAAqE,CAAC;IAC1E,MAAM,aAAa,GAAG,UAAU,CAAO,OAAO;QAC5C,oBAAoB,GAAG,OAAO,CAAC;KAChC,CAAC,CAAC;IAEH,SAAS,kBAAkB,CAAC,UAA4C;QACtE,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YACxC,IAAI,UAAU,KAAK,MAAM,EAAE;gBACzB,OAAO;aACR;YACD,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;YACxE,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;gBAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;aACjC;SACF,CAAC,CAAC;KACJ;IAED,SAAS,qBAAqB;QAC5B,IAAI,0BAA0B,CAAC,MAAM,CAAC,EAAE;YAEtC,kCAAkC,CAAC,MAAM,CAAC,CAAC;YAE3C,MAAM,GAAG,kCAAkC,CAAC,MAAM,CAAC,CAAC;YACpD,kBAAkB,CAAC,MAAM,CAAC,CAAC;SAC5B;QAED,MAAM,WAAW,GAA4B;YAC3C,WAAW,EAAE,KAAK;;;;gBAIhB,cAAc,CAAC;oBACb,mBAAmB,GAAG,KAAK,CAAC;oBAC5B,mBAAmB,GAAG,KAAK,CAAC;oBAE5B,MAAM,MAAM,GAAG,KAAK,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,CAAC;oBACnB,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;wBAC5B,IAAI;4BACF,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;yBACnC;wBAAC,OAAO,MAAM,EAAE;4BACf,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;4BAC7E,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;4BAC7E,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;4BAC3D,OAAO;yBACR;qBACF;oBAED,IAAI,CAAC,SAAS,EAAE;wBACd,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;qBAChF;oBACD,IAAI,CAAC,SAAS,EAAE;wBACd,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;qBAChF;oBAED,OAAO,GAAG,KAAK,CAAC;oBAChB,IAAI,mBAAmB,EAAE;wBACvB,cAAc,EAAE,CAAC;qBAClB;yBAAM,IAAI,mBAAmB,EAAE;wBAC9B,cAAc,EAAE,CAAC;qBAClB;iBACF,CAAC,CAAC;aACJ;YACD,WAAW,EAAE;gBACX,OAAO,GAAG,KAAK,CAAC;gBAChB,IAAI,CAAC,SAAS,EAAE;oBACd,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;iBACtE;gBACD,IAAI,CAAC,SAAS,EAAE;oBACd,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;iBACtE;gBACD,IAAI,OAAO,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClE,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;iBAC3E;gBACD,IAAI,OAAO,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClE,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;iBAC3E;gBACD,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;oBAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBACjC;aACF;YACD,WAAW,EAAE;gBACX,OAAO,GAAG,KAAK,CAAC;aACjB;SACF,CAAC;QACF,+BAA+B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;KACtD;IAED,SAAS,kBAAkB,CAAC,IAAqB,EAAE,UAAmB;QACpE,IAAI,6BAA6B,CAAa,MAAM,CAAC,EAAE;YAErD,kCAAkC,CAAC,MAAM,CAAC,CAAC;YAE3C,MAAM,GAAG,+BAA+B,CAAC,MAAM,CAAC,CAAC;YACjD,kBAAkB,CAAC,MAAM,CAAC,CAAC;SAC5B;QAED,MAAM,UAAU,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC;QAClD,MAAM,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC;QAEnD,MAAM,eAAe,GAAqC;YACxD,WAAW,EAAE,KAAK;;;;gBAIhB,cAAc,CAAC;oBACb,mBAAmB,GAAG,KAAK,CAAC;oBAC5B,mBAAmB,GAAG,KAAK,CAAC;oBAE5B,MAAM,YAAY,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;oBACxD,MAAM,aAAa,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;oBAEzD,IAAI,CAAC,aAAa,EAAE;wBAClB,IAAI,WAAW,CAAC;wBAChB,IAAI;4BACF,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;yBACxC;wBAAC,OAAO,MAAM,EAAE;4BACf,iCAAiC,CAAC,UAAU,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;4BAChF,iCAAiC,CAAC,WAAW,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;4BACjF,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;4BAC3D,OAAO;yBACR;wBACD,IAAI,CAAC,YAAY,EAAE;4BACjB,8CAA8C,CAAC,UAAU,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;yBAC7F;wBACD,mCAAmC,CAAC,WAAW,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;qBACzF;yBAAM,IAAI,CAAC,YAAY,EAAE;wBACxB,8CAA8C,CAAC,UAAU,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;qBAC7F;oBAED,OAAO,GAAG,KAAK,CAAC;oBAChB,IAAI,mBAAmB,EAAE;wBACvB,cAAc,EAAE,CAAC;qBAClB;yBAAM,IAAI,mBAAmB,EAAE;wBAC9B,cAAc,EAAE,CAAC;qBAClB;iBACF,CAAC,CAAC;aACJ;YACD,WAAW,EAAE,KAAK;gBAChB,OAAO,GAAG,KAAK,CAAC;gBAEhB,MAAM,YAAY,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;gBACxD,MAAM,aAAa,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;gBAEzD,IAAI,CAAC,YAAY,EAAE;oBACjB,iCAAiC,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC;iBACzE;gBACD,IAAI,CAAC,aAAa,EAAE;oBAClB,iCAAiC,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;iBAC1E;gBAED,IAAI,KAAK,KAAK,SAAS,EAAE;oBAGvB,IAAI,CAAC,YAAY,EAAE;wBACjB,8CAA8C,CAAC,UAAU,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;qBAC7F;oBACD,IAAI,CAAC,aAAa,IAAI,WAAW,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;wBACxF,mCAAmC,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;qBAC/E;iBACF;gBAED,IAAI,CAAC,YAAY,IAAI,CAAC,aAAa,EAAE;oBACnC,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBACjC;aACF;YACD,WAAW,EAAE;gBACX,OAAO,GAAG,KAAK,CAAC;aACjB;SACF,CAAC;QACF,4BAA4B,CAAC,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;KAC7D;IAED,SAAS,cAAc;QACrB,IAAI,OAAO,EAAE;YACX,mBAAmB,GAAG,IAAI,CAAC;YAC3B,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,OAAO,GAAG,IAAI,CAAC;QAEf,MAAM,WAAW,GAAG,0CAA0C,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAClG,IAAI,WAAW,KAAK,IAAI,EAAE;YACxB,qBAAqB,EAAE,CAAC;SACzB;aAAM;YACL,kBAAkB,CAAC,WAAW,CAAC,KAAM,EAAE,KAAK,CAAC,CAAC;SAC/C;QAED,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,SAAS,cAAc;QACrB,IAAI,OAAO,EAAE;YACX,mBAAmB,GAAG,IAAI,CAAC;YAC3B,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,OAAO,GAAG,IAAI,CAAC;QAEf,MAAM,WAAW,GAAG,0CAA0C,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAClG,IAAI,WAAW,KAAK,IAAI,EAAE;YACxB,qBAAqB,EAAE,CAAC;SACzB;aAAM;YACL,kBAAkB,CAAC,WAAW,CAAC,KAAM,EAAE,IAAI,CAAC,CAAC;SAC9C;QAED,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,SAAS,gBAAgB,CAAC,MAAW;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,MAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,MAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;QACD,OAAO,aAAa,CAAC;KACtB;IAED,SAAS,gBAAgB,CAAC,MAAW;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,MAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,MAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;QACD,OAAO,aAAa,CAAC;KACtB;IAED,SAAS,cAAc;QACrB,OAAO;KACR;IAED,OAAO,GAAG,wBAAwB,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;IACrF,OAAO,GAAG,wBAAwB,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;IAErF,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAE3B,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5B;;SCvagB,oCAAoC,CAClD,MAAyD,EACzD,OAAe;IAEf,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClC,MAAM,QAAQ,GAAG,MAAmD,CAAC;IACrE,MAAM,qBAAqB,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,qBAAqB,CAAC;IAC9D,MAAM,MAAM,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,CAAC;IAChC,MAAM,IAAI,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC;IAC5B,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;IAC9B,MAAM,IAAI,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC;IAC5B,OAAO;QACL,qBAAqB,EAAE,qBAAqB,KAAK,SAAS;YACxD,SAAS;YACT,uCAAuC,CACrC,qBAAqB,EACrB,GAAG,OAAO,0CAA0C,CACrD;QACH,MAAM,EAAE,MAAM,KAAK,SAAS;YAC1B,SAAS;YACT,qCAAqC,CAAC,MAAM,EAAE,QAAS,EAAE,GAAG,OAAO,2BAA2B,CAAC;QACjG,IAAI,EAAE,IAAI,KAAK,SAAS;YACtB,SAAS;YACT,mCAAmC,CAAC,IAAI,EAAE,QAAS,EAAE,GAAG,OAAO,yBAAyB,CAAC;QAC3F,KAAK,EAAE,KAAK,KAAK,SAAS;YACxB,SAAS;YACT,oCAAoC,CAAC,KAAK,EAAE,QAAS,EAAE,GAAG,OAAO,0BAA0B,CAAC;QAC9F,IAAI,EAAE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,yBAAyB,CAAC,IAAI,EAAE,GAAG,OAAO,yBAAyB,CAAC;KAC5G,CAAC;AACJ,CAAC;AAED,SAAS,qCAAqC,CAC5C,EAAkC,EAClC,QAAuC,EACvC,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,MAAW,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED,SAAS,mCAAmC,CAC1C,EAAgD,EAChD,QAA0C,EAC1C,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,UAAuC,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9F,CAAC;AAED,SAAS,oCAAoC,CAC3C,EAAiD,EACjD,QAA0C,EAC1C,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,UAAuC,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9F,CAAC;AAED,SAAS,yBAAyB,CAAC,IAAY,EAAE,OAAe;IAC9D,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;IACjB,IAAI,IAAI,KAAK,OAAO,EAAE;QACpB,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,KAAK,IAAI,2DAA2D,CAAC,CAAC;KACrG;IACD,OAAO,IAAI,CAAC;AACd;;SC1EgB,oBAAoB,CAAC,OAA0D,EAC1D,OAAe;IAClD,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAC;IAC3B,OAAO;QACL,IAAI,EAAE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,+BAA+B,CAAC,IAAI,EAAE,GAAG,OAAO,yBAAyB,CAAC;KAClH,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CAAC,IAAY,EAAE,OAAe;IACpE,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;IACjB,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,KAAK,IAAI,iEAAiE,CAAC,CAAC;KAC3G;IACD,OAAO,IAAI,CAAC;AACd;;SCZgB,sBAAsB,CAAC,OAAyD,EACzD,OAAe;IACpD,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACnC,MAAM,aAAa,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CAAC;IAC7C,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;AACnD;;SCPgB,kBAAkB,CAAC,OAA6C,EAC7C,OAAe;IAChD,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACnC,MAAM,YAAY,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,CAAC;IAC3C,MAAM,aAAa,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CAAC;IAC7C,MAAM,YAAY,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,CAAC;IAC3C,MAAM,MAAM,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAC;IAC/B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,iBAAiB,CAAC,MAAM,EAAE,GAAG,OAAO,2BAA2B,CAAC,CAAC;KAClE;IACD,OAAO;QACL,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC;QACnC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC;QACrC,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC;QACnC,MAAM;KACP,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAe,EAAE,OAAe;IACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;QAC1B,MAAM,IAAI,SAAS,CAAC,GAAG,OAAO,yBAAyB,CAAC,CAAC;KAC1D;AACH;;SCpBgB,2BAA2B,CACzC,IAAuD,EACvD,OAAe;IAEf,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEhC,MAAM,QAAQ,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAC;IAChC,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;IAClE,oBAAoB,CAAC,QAAQ,EAAE,GAAG,OAAO,6BAA6B,CAAC,CAAC;IAExE,MAAM,QAAQ,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,CAAC;IAChC,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;IAClE,oBAAoB,CAAC,QAAQ,EAAE,GAAG,OAAO,6BAA6B,CAAC,CAAC;IAExE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;AAChC;;ACiDA;;;;;MAKa,cAAc;IAczB,YAAY,sBAAqF,EAAE,EACvF,cAAqD,EAAE;QACjE,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACrC,mBAAmB,GAAG,IAAI,CAAC;SAC5B;aAAM;YACL,YAAY,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;SACtD;QAED,MAAM,QAAQ,GAAG,sBAAsB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QACzE,MAAM,gBAAgB,GAAG,oCAAoC,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;QAEtG,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,gBAAgB,CAAC,IAAI,KAAK,OAAO,EAAE;YACrC,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC/B,MAAM,IAAI,UAAU,CAAC,4DAA4D,CAAC,CAAC;aACpF;YACD,MAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACxD,qDAAqD,CACnD,IAAqC,EACrC,gBAAgB,EAChB,aAAa,CACd,CAAC;SACH;aAAM;YAEL,MAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACxD,wDAAwD,CACtD,IAAI,EACJ,gBAAgB,EAChB,aAAa,EACb,aAAa,CACd,CAAC;SACH;KACF;;;;IAKD,IAAI,MAAM;QACR,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAMD,2BAAyB,CAAC,QAAQ,CAAC,CAAC;SAC3C;QAED,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;KACrC;;;;;;;IAQD,MAAM,CAAC,SAAc,SAAS;QAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACjE;QAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAC,CAAC;SAC/F;QAED,OAAO,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC3C;IAqBD,SAAS,CACP,aAAgE,SAAS;QAEzE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAMA,2BAAyB,CAAC,WAAW,CAAC,CAAC;SAC9C;QAED,MAAM,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;QAEpE,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,OAAO,kCAAkC,CAAC,IAAI,CAAC,CAAC;SAGlB;QAChC,OAAO,+BAA+B,CAAC,IAAqC,CAAC,CAAC;KAC/E;IAaD,WAAW,CACT,YAA8E,EAC9E,aAAmD,EAAE;QAErD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAMA,2BAAyB,CAAC,aAAa,CAAC,CAAC;SAChD;QACD,sBAAsB,CAAC,YAAY,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QAEvD,MAAM,SAAS,GAAG,2BAA2B,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;QAC/E,MAAM,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;QAEnE,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,gFAAgF,CAAC,CAAC;SACvG;QACD,IAAI,sBAAsB,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC9C,MAAM,IAAI,SAAS,CAAC,gFAAgF,CAAC,CAAC;SACvG;QAED,MAAM,OAAO,GAAG,oBAAoB,CAClC,IAAI,EAAE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAC5G,CAAC;QAEF,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAEnC,OAAO,SAAS,CAAC,QAAQ,CAAC;KAC3B;IAUD,MAAM,CAAC,WAAiD,EACjD,aAAmD,EAAE;QAC1D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACjE;QAED,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,OAAO,mBAAmB,CAAC,sCAAsC,CAAC,CAAC;SACpE;QACD,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE;YAClC,OAAO,mBAAmB,CACxB,IAAI,SAAS,CAAC,2EAA2E,CAAC,CAC3F,CAAC;SACH;QAED,IAAI,OAAmC,CAAC;QACxC,IAAI;YACF,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;SAC9D;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;QAED,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CACxB,IAAI,SAAS,CAAC,2EAA2E,CAAC,CAC3F,CAAC;SACH;QACD,IAAI,sBAAsB,CAAC,WAAW,CAAC,EAAE;YACvC,OAAO,mBAAmB,CACxB,IAAI,SAAS,CAAC,2EAA2E,CAAC,CAC3F,CAAC;SACH;QAED,OAAO,oBAAoB,CACzB,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CACrG,CAAC;KACH;;;;;;;;;;;;IAaD,GAAG;QACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAMA,2BAAyB,CAAC,KAAK,CAAC,CAAC;SACxC;QAED,MAAM,QAAQ,GAAG,iBAAiB,CAAC,IAAW,CAAC,CAAC;QAChD,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;KACtC;IAcD,MAAM,CAAC,aAA+D,SAAS;QAC7E,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAMA,2BAAyB,CAAC,QAAQ,CAAC,CAAC;SAC3C;QAED,MAAM,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;QACtE,OAAO,kCAAkC,CAAI,IAAI,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;KAC3E;CAMF;AAED,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE;IAChD,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC5B,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC/B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACjC,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC5B,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACzB,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC5B,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC7B,CAAC,CAAC;AACH,IAAI,OAAOD,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAClE,KAAK,EAAE,gBAAgB;QACvB,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AACD,IAAI,OAAOA,cAAM,CAAC,aAAa,KAAK,QAAQ,EAAE;IAC5C,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAEA,cAAM,CAAC,aAAa,EAAE;QACpE,KAAK,EAAE,cAAc,CAAC,SAAS,CAAC,MAAM;QACtC,QAAQ,EAAE,IAAI;QACd,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAkBD;AAEA;SACgB,oBAAoB,CAAI,cAA8C,EAC9C,aAAkC,EAClC,eAA+C,EAC/C,aAAa,GAAG,CAAC,EACjB,gBAAgD,MAAM,CAAC;IAG7F,MAAM,MAAM,GAAsB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC1E,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAuC,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;IAChH,oCAAoC,CAClC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,CACjG,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;SACgB,wBAAwB,CACtC,cAA8C,EAC9C,aAAkC,EAClC,eAA+C;IAE/C,MAAM,MAAM,GAAuB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC3E,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAiC,MAAM,CAAC,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC;IACvG,iCAAiC,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;IAEpH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,wBAAwB,CAAC,MAAsB;IACtD,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;IAC3B,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;IAC3B,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;IAChC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,CAAC;SAEe,gBAAgB,CAAC,CAAU;IACzC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;QACzE,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,cAAc,CAAC;AACrC,CAAC;SAQe,sBAAsB,CAAC,MAAsB;IAG3D,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;QAChC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;SAEgB,oBAAoB,CAAI,MAAyB,EAAE,MAAW;IAC5E,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;IAEzB,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IACD,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;QAC/B,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KACjD;IAED,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAE5B,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,IAAI,0BAA0B,CAAC,MAAM,CAAC,EAAE;QAC9D,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe;YAC9C,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;SACxC,CAAC,CAAC;QACH,MAAM,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;KAC9C;IAED,MAAM,mBAAmB,GAAG,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;IAClF,OAAO,oBAAoB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;AACzD,CAAC;SAEe,mBAAmB,CAAI,MAAyB;IAG9D,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC;IAEzB,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAE9B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO;KACR;IAED,iCAAiC,CAAC,MAAM,CAAC,CAAC;IAE1C,IAAI,6BAA6B,CAAI,MAAM,CAAC,EAAE;QAC5C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW;YACtC,WAAW,CAAC,WAAW,EAAE,CAAC;SAC3B,CAAC,CAAC;QACH,MAAM,CAAC,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC;KAC1C;AACH,CAAC;SAEe,mBAAmB,CAAI,MAAyB,EAAE,CAAM;IAItE,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;IAExB,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAE9B,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO;KACR;IAED,gCAAgC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAE5C,IAAI,6BAA6B,CAAI,MAAM,CAAC,EAAE;QAC5C,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW;YACtC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC5B,CAAC,CAAC;QAEH,MAAM,CAAC,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC;KAC1C;SAAM;QAGL,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe;YAC9C,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAChC,CAAC,CAAC;QAEH,MAAM,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;KAC9C;AACH,CAAC;AAmBD;AAEA,SAASC,2BAAyB,CAAC,IAAY;IAC7C,OAAO,IAAI,SAAS,CAAC,4BAA4B,IAAI,uCAAuC,CAAC,CAAC;AAChG;;SCjhBgB,0BAA0B,CAAC,IAA4C,EAC5C,OAAe;IACxD,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChC,MAAM,aAAa,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;IAC1C,mBAAmB,CAAC,aAAa,EAAE,eAAe,EAAE,qBAAqB,CAAC,CAAC;IAC3E,OAAO;QACL,aAAa,EAAE,yBAAyB,CAAC,aAAa,CAAC;KACxD,CAAC;AACJ;;ACNA;AACA,MAAM,sBAAsB,GAAG,CAAC,KAAsB;IACpD,OAAO,KAAK,CAAC,UAAU,CAAC;AAC1B,CAAC,CAAC;AACF,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE,MAAM,EAAE;IACpD,KAAK,EAAE,MAAM;IACb,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEH;;;;;MAKqB,yBAAyB;IAI5C,YAAY,OAA4B;QACtC,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,2BAA2B,CAAC,CAAC;QAChE,OAAO,GAAG,0BAA0B,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACjE,IAAI,CAAC,uCAAuC,GAAG,OAAO,CAAC,aAAa,CAAC;KACtE;;;;IAKD,IAAI,aAAa;QACf,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,6BAA6B,CAAC,eAAe,CAAC,CAAC;SACtD;QACD,OAAO,IAAI,CAAC,uCAAuC,CAAC;KACrD;;;;IAKD,IAAI,IAAI;QACN,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,6BAA6B,CAAC,MAAM,CAAC,CAAC;SAC7C;QACD,OAAO,sBAAsB,CAAC;KAC/B;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,SAAS,EAAE;IAC3D,aAAa,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACnC,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC3B,CAAC,CAAC;AACH,IAAI,OAAOD,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,yBAAyB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QAC7E,KAAK,EAAE,2BAA2B;QAClC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;AAEA,SAAS,6BAA6B,CAAC,IAAY;IACjD,OAAO,IAAI,SAAS,CAAC,uCAAuC,IAAI,kDAAkD,CAAC,CAAC;AACtH,CAAC;SAEe,2BAA2B,CAAC,CAAM;IAChD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,yCAAyC,CAAC,EAAE;QACvF,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,yBAAyB,CAAC;AAChD;;ACxEA;AACA,MAAM,iBAAiB,GAAG;IACxB,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AACF,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE,MAAM,EAAE;IAC/C,KAAK,EAAE,MAAM;IACb,YAAY,EAAE,IAAI;CACnB,CAAC,CAAC;AAEH;;;;;MAKqB,oBAAoB;IAIvC,YAAY,OAA4B;QACtC,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,sBAAsB,CAAC,CAAC;QAC3D,OAAO,GAAG,0BAA0B,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACjE,IAAI,CAAC,kCAAkC,GAAG,OAAO,CAAC,aAAa,CAAC;KACjE;;;;IAKD,IAAI,aAAa;QACf,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;YACjC,MAAM,wBAAwB,CAAC,eAAe,CAAC,CAAC;SACjD;QACD,OAAO,IAAI,CAAC,kCAAkC,CAAC;KAChD;;;;;IAMD,IAAI,IAAI;QACN,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;YACjC,MAAM,wBAAwB,CAAC,MAAM,CAAC,CAAC;SACxC;QACD,OAAO,iBAAiB,CAAC;KAC1B;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,SAAS,EAAE;IACtD,aAAa,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IACnC,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC3B,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QACxE,KAAK,EAAE,sBAAsB;QAC7B,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;AAEA,SAAS,wBAAwB,CAAC,IAAY;IAC5C,OAAO,IAAI,SAAS,CAAC,kCAAkC,IAAI,6CAA6C,CAAC,CAAC;AAC5G,CAAC;SAEe,sBAAsB,CAAC,CAAM;IAC3C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,oCAAoC,CAAC,EAAE;QAClF,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,oBAAoB,CAAC;AAC3C;;SCnEgB,kBAAkB,CAAO,QAAkC,EAClC,OAAe;IACtD,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACpC,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;IAC9B,MAAM,YAAY,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,YAAY,CAAC;IAC5C,MAAM,KAAK,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC;IAC9B,MAAM,SAAS,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,SAAS,CAAC;IACtC,MAAM,YAAY,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,YAAY,CAAC;IAC5C,OAAO;QACL,KAAK,EAAE,KAAK,KAAK,SAAS;YACxB,SAAS;YACT,+BAA+B,CAAC,KAAK,EAAE,QAAS,EAAE,GAAG,OAAO,0BAA0B,CAAC;QACzF,YAAY;QACZ,KAAK,EAAE,KAAK,KAAK,SAAS;YACxB,SAAS;YACT,+BAA+B,CAAC,KAAK,EAAE,QAAS,EAAE,GAAG,OAAO,0BAA0B,CAAC;QACzF,SAAS,EAAE,SAAS,KAAK,SAAS;YAChC,SAAS;YACT,mCAAmC,CAAC,SAAS,EAAE,QAAS,EAAE,GAAG,OAAO,8BAA8B,CAAC;QACrG,YAAY;KACb,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CACtC,EAA+B,EAC/B,QAA2B,EAC3B,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,UAA+C,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AACtG,CAAC;AAED,SAAS,+BAA+B,CACtC,EAA+B,EAC/B,QAA2B,EAC3B,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,UAA+C,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AACtG,CAAC;AAED,SAAS,mCAAmC,CAC1C,EAAsC,EACtC,QAA2B,EAC3B,OAAe;IAEf,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,CAAC,KAAQ,EAAE,UAA+C,KAAK,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;AACvH;;ACjCA;AAEA;;;;;;;;MAQa,eAAe;IAmB1B,YAAY,iBAAuD,EAAE,EACzD,sBAA6D,EAAE,EAC/D,sBAA6D,EAAE;QACzE,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,cAAc,GAAG,IAAI,CAAC;SACvB;QAED,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;QACzF,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;QAExF,MAAM,WAAW,GAAG,kBAAkB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QAC1E,IAAI,WAAW,CAAC,YAAY,KAAK,SAAS,EAAE;YAC1C,MAAM,IAAI,UAAU,CAAC,gCAAgC,CAAC,CAAC;SACxD;QACD,IAAI,WAAW,CAAC,YAAY,KAAK,SAAS,EAAE;YAC1C,MAAM,IAAI,UAAU,CAAC,gCAAgC,CAAC,CAAC;SACxD;QAED,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACxE,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QACrE,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACxE,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAErE,IAAI,oBAAgE,CAAC;QACrE,MAAM,YAAY,GAAG,UAAU,CAAO,OAAO;YAC3C,oBAAoB,GAAG,OAAO,CAAC;SAChC,CAAC,CAAC;QAEH,yBAAyB,CACvB,IAAI,EAAE,YAAY,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,CAC/G,CAAC;QACF,oDAAoD,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAExE,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE;YACnC,oBAAoB,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;SAC1E;aAAM;YACL,oBAAoB,CAAC,SAAS,CAAC,CAAC;SACjC;KACF;;;;IAKD,IAAI,QAAQ;QACV,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,yBAAyB,CAAC,UAAU,CAAC,CAAC;SAC7C;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;;;IAKD,IAAI,QAAQ;QACV,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,yBAAyB,CAAC,UAAU,CAAC,CAAC;SAC7C;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,SAAS,EAAE;IACjD,QAAQ,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC9B,QAAQ,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAC/B,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QACnE,KAAK,EAAE,iBAAiB;QACxB,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAwCD,SAAS,yBAAyB,CAAO,MAA6B,EAC7B,YAA2B,EAC3B,qBAA6B,EAC7B,qBAAqD,EACrD,qBAA6B,EAC7B,qBAAqD;IAC5F,SAAS,cAAc;QACrB,OAAO,YAAY,CAAC;KACrB;IAED,SAAS,cAAc,CAAC,KAAQ;QAC9B,OAAO,wCAAwC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAChE;IAED,SAAS,cAAc,CAAC,MAAW;QACjC,OAAO,wCAAwC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACjE;IAED,SAAS,cAAc;QACrB,OAAO,wCAAwC,CAAC,MAAM,CAAC,CAAC;KACzD;IAED,MAAM,CAAC,SAAS,GAAG,oBAAoB,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAC9D,qBAAqB,EAAE,qBAAqB,CAAC,CAAC;IAEtF,SAAS,aAAa;QACpB,OAAO,yCAAyC,CAAC,MAAM,CAAC,CAAC;KAC1D;IAED,SAAS,eAAe,CAAC,MAAW;QAClC,2CAA2C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC5D,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,MAAM,CAAC,SAAS,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,qBAAqB,EACrE,qBAAqB,CAAC,CAAC;;IAG/D,MAAM,CAAC,aAAa,GAAG,SAAU,CAAC;IAClC,MAAM,CAAC,0BAA0B,GAAG,SAAU,CAAC;IAC/C,MAAM,CAAC,kCAAkC,GAAG,SAAU,CAAC;IACvD,8BAA8B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAE7C,MAAM,CAAC,0BAA0B,GAAG,SAAU,CAAC;AACjD,CAAC;AAED,SAAS,iBAAiB,CAAC,CAAU;IACnC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,4BAA4B,CAAC,EAAE;QAC1E,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,eAAe,CAAC;AACtC,CAAC;AAED;AACA,SAAS,oBAAoB,CAAC,MAAuB,EAAE,CAAM;IAC3D,oCAAoC,CAClC,MAAM,CAAC,SAAS,CAAC,yBAAiE,EAClF,CAAC,CACF,CAAC;IACF,2CAA2C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,2CAA2C,CAAC,MAAuB,EAAE,CAAM;IAClF,+CAA+C,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;IACnF,4CAA4C,CAAC,MAAM,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;IAC5F,IAAI,MAAM,CAAC,aAAa,EAAE;;;;QAIxB,8BAA8B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC/C;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,MAAuB,EAAE,YAAqB;;IAIpF,IAAI,MAAM,CAAC,0BAA0B,KAAK,SAAS,EAAE;QACnD,MAAM,CAAC,kCAAkC,EAAE,CAAC;KAC7C;IAED,MAAM,CAAC,0BAA0B,GAAG,UAAU,CAAC,OAAO;QACpD,MAAM,CAAC,kCAAkC,GAAG,OAAO,CAAC;KACrD,CAAC,CAAC;IAEH,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC;AACtC,CAAC;AAED;AAEA;;;;;MAKa,gCAAgC;IAQ3C;QACE,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;;;;IAKD,IAAI,WAAW;QACb,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,oCAAoC,CAAC,aAAa,CAAC,CAAC;SAC3D;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,yBAAyB,CAAC;QAC/F,OAAO,6CAA6C,CAAC,kBAAwD,CAAC,CAAC;KAChH;IAMD,OAAO,CAAC,QAAW,SAAU;QAC3B,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,oCAAoC,CAAC,SAAS,CAAC,CAAC;SACvD;QAED,uCAAuC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACtD;;;;;IAMD,KAAK,CAAC,SAAc,SAAS;QAC3B,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,oCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;QAED,qCAAqC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACrD;;;;;IAMD,SAAS;QACP,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,oCAAoC,CAAC,WAAW,CAAC,CAAC;SACzD;QAED,yCAAyC,CAAC,IAAI,CAAC,CAAC;KACjD;CACF;AAED,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,SAAS,EAAE;IAClE,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC7B,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC3B,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;IAC/B,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;CAClC,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,gCAAgC,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;QACpF,KAAK,EAAE,kCAAkC;QACzC,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;CACJ;AAED;AAEA,SAAS,kCAAkC,CAAU,CAAM;IACzD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,4BAA4B,CAAC,EAAE;QAC1E,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,gCAAgC,CAAC;AACvD,CAAC;AAED,SAAS,qCAAqC,CAAO,MAA6B,EAC7B,UAA+C,EAC/C,kBAA+C,EAC/C,cAAmC;IAItF,UAAU,CAAC,0BAA0B,GAAG,MAAM,CAAC;IAC/C,MAAM,CAAC,0BAA0B,GAAG,UAAU,CAAC;IAE/C,UAAU,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IACpD,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;AAC9C,CAAC;AAED,SAAS,oDAAoD,CAAO,MAA6B,EAC7B,WAAuC;IACzG,MAAM,UAAU,GAAwC,MAAM,CAAC,MAAM,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;IAElH,IAAI,kBAAkB,GAAG,CAAC,KAAQ;QAChC,IAAI;YACF,uCAAuC,CAAC,UAAU,EAAE,KAAqB,CAAC,CAAC;YAC3E,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;QAAC,OAAO,gBAAgB,EAAE;YACzB,OAAO,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;SAC9C;KACF,CAAC;IAEF,IAAI,cAAc,GAAwB,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAE/E,IAAI,WAAW,CAAC,SAAS,KAAK,SAAS,EAAE;QACvC,kBAAkB,GAAG,KAAK,IAAI,WAAW,CAAC,SAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;KACzE;IACD,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE;QACnC,cAAc,GAAG,MAAM,WAAW,CAAC,KAAM,CAAC,UAAU,CAAC,CAAC;KACvD;IAED,qCAAqC,CAAC,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;AAChG,CAAC;AAED,SAAS,+CAA+C,CAAC,UAAiD;IACxG,UAAU,CAAC,mBAAmB,GAAG,SAAU,CAAC;IAC5C,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;AAC1C,CAAC;AAED,SAAS,uCAAuC,CAAI,UAA+C,EAAE,KAAQ;IAC3G,MAAM,MAAM,GAAG,UAAU,CAAC,0BAA0B,CAAC;IACrD,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,yBAA+D,CAAC;IAC5G,IAAI,CAAC,gDAAgD,CAAC,kBAAkB,CAAC,EAAE;QACzE,MAAM,IAAI,SAAS,CAAC,sDAAsD,CAAC,CAAC;KAC7E;;;IAKD,IAAI;QACF,sCAAsC,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;KACnE;IAAC,OAAO,CAAC,EAAE;;QAEV,2CAA2C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAEvD,MAAM,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;KACrC;IAED,MAAM,YAAY,GAAG,8CAA8C,CAAC,kBAAkB,CAAC,CAAC;IACxF,IAAI,YAAY,KAAK,MAAM,CAAC,aAAa,EAAE;QAEzC,8BAA8B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KAC9C;AACH,CAAC;AAED,SAAS,qCAAqC,CAAC,UAAiD,EAAE,CAAM;IACtG,oBAAoB,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,gDAAgD,CAAO,UAA+C,EAC/C,KAAQ;IACtE,MAAM,gBAAgB,GAAG,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC/D,OAAO,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;QACxD,oBAAoB,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;QAC/D,MAAM,CAAC,CAAC;KACT,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yCAAyC,CAAI,UAA+C;IACnG,MAAM,MAAM,GAAG,UAAU,CAAC,0BAA0B,CAAC;IACrD,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,yBAA+D,CAAC;IAE5G,oCAAoC,CAAC,kBAAkB,CAAC,CAAC;IAEzD,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;IAC1D,2CAA2C,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;AAEA,SAAS,wCAAwC,CAAO,MAA6B,EAAE,KAAQ;IAG7F,MAAM,UAAU,GAAG,MAAM,CAAC,0BAA0B,CAAC;IAErD,IAAI,MAAM,CAAC,aAAa,EAAE;QACxB,MAAM,yBAAyB,GAAG,MAAM,CAAC,0BAA0B,CACnB;QAChD,OAAO,oBAAoB,CAAC,yBAAyB,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;YAClC,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC9B,IAAI,KAAK,KAAK,UAAU,EAAE;gBACxB,MAAM,QAAQ,CAAC,YAAY,CAAC;aAED;YAC7B,OAAO,gDAAgD,CAAO,UAAU,EAAE,KAAK,CAAC,CAAC;SAClF,CAAC,CAAC;KACJ;IAED,OAAO,gDAAgD,CAAO,UAAU,EAAE,KAAK,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,wCAAwC,CAAC,MAAuB,EAAE,MAAW;;;IAGpF,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrC,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,wCAAwC,CAAO,MAA6B;;IAEnF,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;IAElC,MAAM,UAAU,GAAG,MAAM,CAAC,0BAA0B,CAAC;IACrD,MAAM,YAAY,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;IAClD,+CAA+C,CAAC,UAAU,CAAC,CAAC;;IAG5D,OAAO,oBAAoB,CAAC,YAAY,EAAE;QACxC,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;YACjC,MAAM,QAAQ,CAAC,YAAY,CAAC;SAC7B;QACD,oCAAoC,CAAC,QAAQ,CAAC,yBAA+D,CAAC,CAAC;KAChH,EAAE,CAAC;QACF,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,QAAQ,CAAC,YAAY,CAAC;KAC7B,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,yCAAyC,CAAC,MAAuB;;IAMxE,8BAA8B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;;IAG9C,OAAO,MAAM,CAAC,0BAA0B,CAAC;AAC3C,CAAC;AAED;AAEA,SAAS,oCAAoC,CAAC,IAAY;IACxD,OAAO,IAAI,SAAS,CAClB,8CAA8C,IAAI,yDAAyD,CAAC,CAAC;AACjH,CAAC;AAED;AAEA,SAAS,yBAAyB,CAAC,IAAY;IAC7C,OAAO,IAAI,SAAS,CAClB,6BAA6B,IAAI,wCAAwC,CAAC,CAAC;AAC/E;;ACzfA,MAAM,OAAO,GAAG;IACd,cAAc;IACd,+BAA+B;IAC/B,4BAA4B;IAC5B,yBAAyB;IACzB,2BAA2B;IAC3B,wBAAwB;IAExB,cAAc;IACd,+BAA+B;IAC/B,2BAA2B;IAE3B,yBAAyB;IACzB,oBAAoB;IAEpB,eAAe;IACf,gCAAgC;CACjC,CAAC;AAEF;AACA,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;IAClC,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;QAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;YACvD,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE;gBACnC,KAAK,EAAE,OAAO,CAAC,IAA8B,CAAC;gBAC9C,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;SACJ;KACF;;;;;"}