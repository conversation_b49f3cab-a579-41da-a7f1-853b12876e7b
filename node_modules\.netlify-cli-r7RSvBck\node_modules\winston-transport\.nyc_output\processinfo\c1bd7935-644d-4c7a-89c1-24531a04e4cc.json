{"parent": null, "pid": 98505, "argv": ["/Users/<USER>/.nvm/versions/node/v16.18.0/bin/node", "/Users/<USER>/winston-transport/node_modules/.bin/mocha", "test/index.test.js", "test/inheritance.test.js", "test/legacy.test.js"], "execArgv": [], "cwd": "/Users/<USER>/winston-transport", "time": 1707030331549, "ppid": 98504, "coverageFilename": "/Users/<USER>/winston-transport/.nyc_output/c1bd7935-644d-4c7a-89c1-24531a04e4cc.json", "externalId": "", "uuid": "c1bd7935-644d-4c7a-89c1-24531a04e4cc", "files": ["/Users/<USER>/winston-transport/index.js", "/Users/<USER>/winston-transport/modern.js", "/Users/<USER>/winston-transport/legacy.js"]}