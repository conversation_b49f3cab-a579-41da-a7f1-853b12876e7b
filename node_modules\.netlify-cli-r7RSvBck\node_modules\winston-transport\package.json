{"name": "winston-transport", "description": "Base stream implementations for winston@3 and up.", "version": "4.7.0", "main": "index.js", "browser": "dist/index.js", "scripts": {"lint": "eslint test/*.js index.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "pretest": "npm run lint && npm run build", "test": "nyc mocha test/*.test.js", "report": "nyc report --reporter=lcov", "build": "rimraf dist && babel *.js -d ./dist", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "**************:winstonjs/winston-transport.git"}, "keywords": ["winston", "transport", "winston3"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/winstonjs/winston-transport/issues"}, "homepage": "https://github.com/winstonjs/winston-transport#readme", "dependencies": {"logform": "^2.3.2", "readable-stream": "^3.6.0", "triple-beam": "^1.3.0"}, "devDependencies": {"@types/node": "^20.8.6", "abstract-winston-transport": ">=0.5.1", "assume": "^2.3.0", "babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "deep-equal": "^2.0.5", "eslint": "^8.8.0", "@dabh/eslint-config-populist": "^5.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "rimraf": "^5.0.5", "winston-compat": "^0.1.5"}, "engines": {"node": ">= 12.0.0"}}