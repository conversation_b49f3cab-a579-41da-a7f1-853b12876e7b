{"name": "json-parse-even-better-errors", "version": "2.3.1", "description": "JSON.parse with context information on error", "main": "index.js", "files": ["*.js"], "scripts": {"preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags", "test": "tap", "snap": "tap"}, "repository": "https://github.com/npm/json-parse-even-better-errors", "keywords": ["JSON", "parser"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "maybekatz"}, "license": "MIT", "devDependencies": {"tap": "^14.6.5"}, "tap": {"check-coverage": true}}