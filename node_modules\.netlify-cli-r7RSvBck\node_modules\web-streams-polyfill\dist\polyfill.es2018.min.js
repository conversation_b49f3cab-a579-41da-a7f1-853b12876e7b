!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";const t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function r(){}const o="undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0;function n(e){return"object"==typeof e&&null!==e||"function"==typeof e}const a=r,i=Promise,l=Promise.prototype.then,s=Promise.resolve.bind(i),u=Promise.reject.bind(i);function c(e){return new i(e)}function d(e){return s(e)}function f(e){return u(e)}function b(e,t,r){return l.call(e,t,r)}function _(e,t,r){b(b(e,t,r),void 0,a)}function h(e,t){_(e,t)}function m(e,t){_(e,void 0,t)}function p(e,t,r){return b(e,t,r)}function y(e){b(e,void 0,a)}const g=(()=>{const e=o&&o.queueMicrotask;if("function"==typeof e)return e;const t=d(void 0);return e=>b(t,e)})();function S(e,t,r){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function v(e,t,r){try{return d(S(e,t,r))}catch(e){return f(e)}}class w{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}function R(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?q(e):"closed"===t._state?function(e){q(e),O(e)}(e):E(e,t._storedError)}function T(e,t){return br(e._ownerReadableStream,t)}function C(e){"readable"===e._ownerReadableStream._state?W(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){E(e,t)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function P(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function q(e){e._closedPromise=c(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function E(e,t){q(e),W(e,t)}function W(e,t){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function O(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const B=t("[[AbortSteps]]"),k=t("[[ErrorSteps]]"),j=t("[[CancelSteps]]"),A=t("[[PullSteps]]"),z=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function L(e,t){if(void 0!==e&&("object"!=typeof(r=e)&&"function"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function F(e,t){if("function"!=typeof e)throw new TypeError(`${t} is not a function.`)}function I(e,t){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function $(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function M(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function Q(e){return Number(e)}function Y(e){return 0===e?0:e}function x(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=Y(o),!z(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return Y(D(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return z(o)&&0!==o?o:0}function N(e,t){if(!dr(e))throw new TypeError(`${t} is not a ReadableStream.`)}function H(e){return new ReadableStreamDefaultReader(e)}function V(e,t){e._reader._readRequests.push(t)}function U(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function G(e){return e._reader._readRequests.length}function X(e){const t=e._reader;return void 0!==t&&!!J(t)}class ReadableStreamDefaultReader{constructor(e){if($(e,1,"ReadableStreamDefaultReader"),N(e,"First parameter"),fr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");R(this,e),this._readRequests=new w}get closed(){return J(this)?this._closedPromise:f(Z("closed"))}cancel(e){return J(this)?void 0===this._ownerReadableStream?f(P("cancel")):T(this,e):f(Z("cancel"))}read(){if(!J(this))return f(Z("read"));if(void 0===this._ownerReadableStream)return f(P("read from"));let e,t;const r=c(((r,o)=>{e=r,t=o}));return K(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!J(this))throw Z("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");C(this)}}}function J(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof ReadableStreamDefaultReader)}function K(e,t){const r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[A](t)}function Z(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,t.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});const ee=Object.getPrototypeOf(Object.getPrototypeOf((async function*(){})).prototype);class te{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?p(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?p(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;if(void 0===e._ownerReadableStream)return f(P("iterate"));let t,r;const o=c(((e,o)=>{t=e,r=o}));return K(e,{_chunkSteps:e=>{this._ongoingPromise=void 0,g((()=>t({value:e,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,C(e),t({value:void 0,done:!0})},_errorSteps:t=>{this._ongoingPromise=void 0,this._isFinished=!0,C(e),r(t)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(void 0===t._ownerReadableStream)return f(P("finish iterating"));if(!this._preventCancel){const r=T(t,e);return C(t),p(r,(()=>({value:e,done:!0})))}return C(t),d({value:e,done:!0})}}const re={next(){return oe(this)?this._asyncIteratorImpl.next():f(ne("next"))},return(e){return oe(this)?this._asyncIteratorImpl.return(e):f(ne("return"))}};function oe(e){if(!n(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof te}catch(e){return!1}}function ne(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}void 0!==ee&&Object.setPrototypeOf(re,ee);const ae=Number.isNaN||function(e){return e!=e};function ie(e){return e.slice()}function le(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}function se(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return le(n,0,e,t,o),n}function ue(e){const t=se(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function ce(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function de(e,t,r){if("number"!=typeof(o=r)||ae(o)||o<0||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function fe(e){e._queue=new w,e._queueTotalSize=0}class ReadableStreamBYOBRequest{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!_e(this))throw De("view");return this._view}respond(e){if(!_e(this))throw De("respond");if($(e,1,"respond"),e=x(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");this._view.buffer,je(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!_e(this))throw De("respondWithNewView");if($(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");e.buffer,Ae(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,t.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!be(this))throw Le("byobRequest");return Be(this)}get desiredSize(){if(!be(this))throw Le("desiredSize");return ke(this)}close(){if(!be(this))throw Le("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);Ee(this)}enqueue(e){if(!be(this))throw Le("enqueue");if($(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);We(this,e)}error(e){if(!be(this))throw Le("error");Oe(this,e)}[j](e){me(this),fe(this);const t=this._cancelAlgorithm(e);return qe(this),t}[A](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0){const t=this._queue.shift();this._queueTotalSize-=t.byteLength,we(this);const r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);return void e._chunkSteps(r)}const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}V(t,e),he(this)}}function be(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof ReadableByteStreamController)}function _e(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ReadableStreamBYOBRequest)}function he(e){if(!function(e){const t=e._controlledReadableByteStream;if("readable"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(X(t)&&G(t)>0)return!0;if(Me(t)&&$e(t)>0)return!0;if(ke(e)>0)return!0;return!1}(e))return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;_(e._pullAlgorithm(),(()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,he(e))}),(t=>{Oe(e,t)}))}function me(e){Re(e),e._pendingPullIntos=new w}function pe(e,t){let r=!1;"closed"===e._state&&(r=!0);const o=ye(t);"default"===t.readerType?U(e,o,r):function(e,t,r){const o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function ye(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function ge(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function Se(e,t){const r=t.elementSize,o=t.bytesFilled-t.bytesFilled%r,n=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+n,i=a-a%r;let l=n,s=!1;i>o&&(l=i-t.bytesFilled,s=!0);const u=e._queue;for(;l>0;){const r=u.peek(),o=Math.min(l,r.byteLength),n=t.byteOffset+t.bytesFilled;le(t.buffer,n,r.buffer,r.byteOffset,o),r.byteLength===o?u.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,ve(e,o,t),l-=o}return s}function ve(e,t,r){r.bytesFilled+=t}function we(e){0===e._queueTotalSize&&e._closeRequested?(qe(e),_r(e._controlledReadableByteStream)):he(e)}function Re(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Te(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();Se(e,t)&&(Pe(e),pe(e._controlledReadableByteStream,t))}}function Ce(e,t){const r=e._pendingPullIntos.peek();Re(e);"closed"===e._controlledReadableByteStream._state?function(e,t){const r=e._controlledReadableByteStream;if(Me(r))for(;$e(r)>0;)pe(r,Pe(e))}(e):function(e,t,r){if(ve(0,t,r),r.bytesFilled<r.elementSize)return;Pe(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled,n=se(r.buffer,t-o,t);ge(e,n,0,n.byteLength)}r.bytesFilled-=o,pe(e._controlledReadableByteStream,r),Te(e)}(e,t,r),he(e)}function Pe(e){return e._pendingPullIntos.shift()}function qe(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Ee(e){const t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state)if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){if(e._pendingPullIntos.peek().bytesFilled>0){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Oe(e,t),t}}qe(e),_r(t)}}function We(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;const o=t.buffer,n=t.byteOffset,a=t.byteLength,i=o;if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();t.buffer,0,t.buffer=t.buffer}if(Re(e),X(r))if(0===G(r))ge(e,i,n,a);else{e._pendingPullIntos.length>0&&Pe(e);U(r,new Uint8Array(i,n,a),!1)}else Me(r)?(ge(e,i,n,a),Te(e)):ge(e,i,n,a);he(e)}function Oe(e,t){const r=e._controlledReadableByteStream;"readable"===r._state&&(me(e),fe(e),qe(e),hr(r,t))}function Be(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}function ke(e){const t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function je(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=r.buffer,Ce(e,t)}function Ae(e,t){const r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const o=t.byteLength;r.buffer=t.buffer,Ce(e,o)}function ze(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,fe(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new w,e._readableStreamController=t;_(d(r()),(()=>{t._started=!0,he(t)}),(e=>{Oe(t,e)}))}function De(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function Le(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function Fe(e){return new ReadableStreamBYOBReader(e)}function Ie(e,t){e._reader._readIntoRequests.push(t)}function $e(e){return e._reader._readIntoRequests.length}function Me(e){const t=e._reader;return void 0!==t&&!!Qe(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,t.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if($(e,1,"ReadableStreamBYOBReader"),N(e,"First parameter"),fr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!be(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");R(this,e),this._readIntoRequests=new w}get closed(){return Qe(this)?this._closedPromise:f(xe("closed"))}cancel(e){return Qe(this)?void 0===this._ownerReadableStream?f(P("cancel")):T(this,e):f(xe("cancel"))}read(e){if(!Qe(this))return f(xe("read"));if(!ArrayBuffer.isView(e))return f(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return f(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return f(new TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return f(P("read from"));let t,r;const o=c(((e,o)=>{t=e,r=o}));return Ye(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),o}releaseLock(){if(!Qe(this))throw xe("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");C(this)}}}function Qe(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof ReadableStreamBYOBReader)}function Ye(e,t,r){const o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?r._errorSteps(o._storedError):function(e,t,r){const o=e._controlledReadableByteStream;let n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);const a=t.constructor,i=t.buffer,l={buffer:i,bufferByteLength:i.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void Ie(o,r);if("closed"!==o._state){if(e._queueTotalSize>0){if(Se(e,l)){const t=ye(l);return we(e),void r._chunkSteps(t)}if(e._closeRequested){const t=new TypeError("Insufficient bytes to fill elements in the given buffer");return Oe(e,t),void r._errorSteps(t)}}e._pendingPullIntos.push(l),Ie(o,r),he(e)}else{const e=new a(l.buffer,l.byteOffset,0);r._closeSteps(e)}}(o._readableStreamController,t,r)}function xe(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function Ne(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(ae(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function He(e){const{size:t}=e;return t||(()=>1)}function Ve(e,t){L(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:Q(r),size:void 0===o?void 0:Ue(o,`${t} has member 'size' that`)}}function Ue(e,t){return F(e,t),t=>Q(e(t))}function Ge(e,t,r){return F(e,r),r=>v(e,t,[r])}function Xe(e,t,r){return F(e,r),()=>v(e,t,[])}function Je(e,t,r){return F(e,r),r=>S(e,t,[r])}function Ke(e,t,r){return F(e,r),(r,o)=>v(e,t,[r,o])}function Ze(e,t){if(!ot(e))throw new TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,t.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});const et="function"==typeof AbortController;class WritableStream{constructor(e={},t={}){void 0===e?e=null:I(e,"First parameter");const r=Ve(t,"Second parameter"),o=function(e,t){L(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:Ge(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:Xe(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:Je(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:Ke(i,e,`${t} has member 'write' that`),type:a}}(e,"First parameter");rt(this);if(void 0!==o.type)throw new RangeError("Invalid type is specified");const n=He(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a=()=>{},i=()=>d(void 0),l=()=>d(void 0),s=()=>d(void 0);void 0!==t.start&&(a=()=>t.start(n));void 0!==t.write&&(i=e=>t.write(e,n));void 0!==t.close&&(l=()=>t.close());void 0!==t.abort&&(s=e=>t.abort(e));vt(e,n,a,i,l,s,r,o)}(this,o,Ne(r,1),n)}get locked(){if(!ot(this))throw Et("locked");return nt(this)}abort(e){return ot(this)?nt(this)?f(new TypeError("Cannot abort a stream that already has a writer")):at(this,e):f(Et("abort"))}close(){return ot(this)?nt(this)?f(new TypeError("Cannot close a stream that already has a writer")):ct(this)?f(new TypeError("Cannot close an already-closing stream")):it(this):f(Et("close"))}getWriter(){if(!ot(this))throw Et("getWriter");return tt(this)}}function tt(e){return new WritableStreamDefaultWriter(e)}function rt(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new w,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function ot(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof WritableStream)}function nt(e){return void 0!==e._writer}function at(e,t){var r;if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort();const o=e._state;if("closed"===o||"errored"===o)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);const a=c(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||st(e,t),a}function it(e){const t=e._state;if("closed"===t||"errored"===t)return f(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=c(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&"writable"===t&&$t(o),de(n=e._writableStreamController,gt,0),Tt(n),r}function lt(e,t){"writable"!==e._state?ut(e):st(e,t)}function st(e,t){const r=e._writableStreamController;e._state="erroring",e._storedError=t;const o=e._writer;void 0!==o&&mt(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&ut(e)}function ut(e){e._state="errored",e._writableStreamController[k]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new w,void 0===e._pendingAbortRequest)return void dt(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void dt(e);_(e._writableStreamController[B](r._reason),(()=>{r._resolve(),dt(e)}),(t=>{r._reject(t),dt(e)}))}function ct(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function dt(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&At(t,e._storedError)}function ft(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){Dt(e)}(r):$t(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(WritableStream.prototype,t.toStringTag,{value:"WritableStream",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if($(e,1,"WritableStreamDefaultWriter"),Ze(e,"First parameter"),nt(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;const t=e._state;if("writable"===t)!ct(e)&&e._backpressure?Dt(this):Ft(this),kt(this);else if("erroring"===t)Lt(this,e._storedError),kt(this);else if("closed"===t)Ft(this),kt(r=this),zt(r);else{const t=e._storedError;Lt(this,t),jt(this,t)}var r}get closed(){return bt(this)?this._closedPromise:f(Ot("closed"))}get desiredSize(){if(!bt(this))throw Ot("desiredSize");if(void 0===this._ownerWritableStream)throw Bt("desiredSize");return function(e){const t=e._ownerWritableStream,r=t._state;if("errored"===r||"erroring"===r)return null;if("closed"===r)return 0;return Rt(t._writableStreamController)}(this)}get ready(){return bt(this)?this._readyPromise:f(Ot("ready"))}abort(e){return bt(this)?void 0===this._ownerWritableStream?f(Bt("abort")):function(e,t){return at(e._ownerWritableStream,t)}(this,e):f(Ot("abort"))}close(){if(!bt(this))return f(Ot("close"));const e=this._ownerWritableStream;return void 0===e?f(Bt("close")):ct(e)?f(new TypeError("Cannot close an already-closing stream")):_t(this)}releaseLock(){if(!bt(this))throw Ot("releaseLock");void 0!==this._ownerWritableStream&&pt(this)}write(e){return bt(this)?void 0===this._ownerWritableStream?f(Bt("write to")):yt(this,e):f(Ot("write"))}}function bt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof WritableStreamDefaultWriter)}function _t(e){return it(e._ownerWritableStream)}function ht(e,t){"pending"===e._closedPromiseState?At(e,t):function(e,t){jt(e,t)}(e,t)}function mt(e,t){"pending"===e._readyPromiseState?It(e,t):function(e,t){Lt(e,t)}(e,t)}function pt(e){const t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");mt(e,r),ht(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function yt(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return Ct(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return f(Bt("write to"));const a=r._state;if("errored"===a)return f(r._storedError);if(ct(r)||"closed"===a)return f(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return f(r._storedError);const i=function(e){return c(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{de(e,t,r)}catch(t){return void Ct(e,t)}const o=e._controlledWritableStream;if(!ct(o)&&"writable"===o._state){ft(o,Pt(e))}Tt(e)}(o,t,n),i}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,t.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const gt={};class WritableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!St(this))throw Wt("abortReason");return this._abortReason}get signal(){if(!St(this))throw Wt("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!St(this))throw Wt("error");"writable"===this._controlledWritableStream._state&&qt(this,e)}[B](e){const t=this._abortAlgorithm(e);return wt(this),t}[k](){fe(this)}}function St(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof WritableStreamDefaultController)}function vt(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,fe(t),t._abortReason=void 0,t._abortController=function(){if(et)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=Pt(t);ft(e,s);_(d(r()),(()=>{t._started=!0,Tt(t)}),(r=>{t._started=!0,lt(e,r)}))}function wt(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Rt(e){return e._strategyHWM-e._queueTotalSize}function Tt(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void ut(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===gt?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),ce(e);const r=e._closeAlgorithm();wt(e),_(r,(()=>{!function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";const t=e._writer;void 0!==t&&zt(t)}(t)}),(e=>{!function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),lt(e,t)}(t,e)}))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);_(e._writeAlgorithm(t),(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(ce(e),!ct(r)&&"writable"===t){const t=Pt(e);ft(r,t)}Tt(e)}),(t=>{"writable"===r._state&&wt(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,lt(e,t)}(r,t)}))}(e,r)}function Ct(e,t){"writable"===e._controlledWritableStream._state&&qt(e,t)}function Pt(e){return Rt(e)<=0}function qt(e,t){const r=e._controlledWritableStream;wt(e),st(r,t)}function Et(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function Wt(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function Ot(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function Bt(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function kt(e){e._closedPromise=c(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function jt(e,t){kt(e),At(e,t)}function At(e,t){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function zt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Dt(e){e._readyPromise=c(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function Lt(e,t){Dt(e),It(e,t)}function Ft(e){Dt(e),$t(e)}function It(e,t){void 0!==e._readyPromise_reject&&(y(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function $t(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,t.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const Mt="undefined"!=typeof DOMException?DOMException:void 0;const Qt=function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(Mt)?Mt:function(){const e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function Yt(e,t,o,n,a,i){const l=H(e),s=tt(t);e._disturbed=!0;let u=!1,p=d(void 0);return c(((g,S)=>{let v;if(void 0!==i){if(v=()=>{const r=new Qt("Aborted","AbortError"),o=[];n||o.push((()=>"writable"===t._state?at(t,r):d(void 0))),a||o.push((()=>"readable"===e._state?br(e,r):d(void 0))),E((()=>Promise.all(o.map((e=>e())))),!0,r)},i.aborted)return void v();i.addEventListener("abort",v)}var w,R,T;if(q(e,l._closedPromise,(e=>{n?W(!0,e):E((()=>at(t,e)),!0,e)})),q(t,s._closedPromise,(t=>{a?W(!0,t):E((()=>br(e,t)),!0,t)})),w=e,R=l._closedPromise,T=()=>{o?W():E((()=>function(e){const t=e._ownerWritableStream,r=t._state;return ct(t)||"closed"===r?d(void 0):"errored"===r?f(t._storedError):_t(e)}(s)))},"closed"===w._state?T():h(R,T),ct(t)||"closed"===t._state){const t=new TypeError("the destination writable stream closed before all data could be piped to it");a?W(!0,t):E((()=>br(e,t)),!0,t)}function P(){const e=p;return b(p,(()=>e!==p?P():void 0))}function q(e,t,r){"errored"===e._state?r(e._storedError):m(t,r)}function E(e,r,o){function n(){_(e(),(()=>O(r,o)),(e=>O(!0,e)))}u||(u=!0,"writable"!==t._state||ct(t)?n():h(P(),n))}function W(e,r){u||(u=!0,"writable"!==t._state||ct(t)?O(e,r):h(P(),(()=>O(e,r))))}function O(e,t){pt(s),C(l),void 0!==i&&i.removeEventListener("abort",v),e?S(t):g(void 0)}y(c(((e,t)=>{!function o(n){n?e():b(u?d(!0):b(s._readyPromise,(()=>c(((e,t)=>{K(l,{_chunkSteps:t=>{p=b(yt(s,t),void 0,r),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})))),o,t)}(!1)})))}))}class ReadableStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!xt(this))throw er("desiredSize");return Jt(this)}close(){if(!xt(this))throw er("close");if(!Kt(this))throw new TypeError("The stream is not in a state that permits close");Ut(this)}enqueue(e){if(!xt(this))throw er("enqueue");if(!Kt(this))throw new TypeError("The stream is not in a state that permits enqueue");return Gt(this,e)}error(e){if(!xt(this))throw er("error");Xt(this,e)}[j](e){fe(this);const t=this._cancelAlgorithm(e);return Vt(this),t}[A](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=ce(this);this._closeRequested&&0===this._queue.length?(Vt(this),_r(t)):Nt(this),e._chunkSteps(r)}else V(t,e),Nt(this)}}function xt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof ReadableStreamDefaultController)}function Nt(e){if(!Ht(e))return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;_(e._pullAlgorithm(),(()=>{e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Nt(e))}),(t=>{Xt(e,t)}))}function Ht(e){const t=e._controlledReadableStream;if(!Kt(e))return!1;if(!e._started)return!1;if(fr(t)&&G(t)>0)return!0;return Jt(e)>0}function Vt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Ut(e){if(!Kt(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(Vt(e),_r(t))}function Gt(e,t){if(!Kt(e))return;const r=e._controlledReadableStream;if(fr(r)&&G(r)>0)U(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw Xt(e,t),t}try{de(e,t,r)}catch(t){throw Xt(e,t),t}}Nt(e)}function Xt(e,t){const r=e._controlledReadableStream;"readable"===r._state&&(fe(e),Vt(e),hr(r,t))}function Jt(e){const t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Kt(e){const t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function Zt(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,fe(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t;_(d(r()),(()=>{t._started=!0,Nt(t)}),(e=>{Xt(t,e)}))}function er(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tr(e,t){return be(e._readableStreamController)?function(e){let t,r,o,n,a,i=H(e),l=!1,s=!1,u=!1,f=!1,b=!1;const _=c((e=>{a=e}));function h(e){m(e._closedPromise,(t=>{e===i&&(Oe(o._readableStreamController,t),Oe(n._readableStreamController,t),f&&b||a(void 0))}))}function p(){Qe(i)&&(C(i),i=H(e),h(i));K(i,{_chunkSteps:t=>{g((()=>{s=!1,u=!1;const r=t;let i=t;if(!f&&!b)try{i=ue(t)}catch(t){return Oe(o._readableStreamController,t),Oe(n._readableStreamController,t),void a(br(e,t))}f||We(o._readableStreamController,r),b||We(n._readableStreamController,i),l=!1,s?S():u&&v()}))},_closeSteps:()=>{l=!1,f||Ee(o._readableStreamController),b||Ee(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&je(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&je(n._readableStreamController,0),f&&b||a(void 0)},_errorSteps:()=>{l=!1}})}function y(t,r){J(i)&&(C(i),i=Fe(e),h(i));const c=r?n:o,d=r?o:n;Ye(i,t,{_chunkSteps:t=>{g((()=>{s=!1,u=!1;const o=r?b:f;if(r?f:b)o||Ae(c._readableStreamController,t);else{let r;try{r=ue(t)}catch(t){return Oe(c._readableStreamController,t),Oe(d._readableStreamController,t),void a(br(e,t))}o||Ae(c._readableStreamController,t),We(d._readableStreamController,r)}l=!1,s?S():u&&v()}))},_closeSteps:e=>{l=!1;const t=r?b:f,o=r?f:b;t||Ee(c._readableStreamController),o||Ee(d._readableStreamController),void 0!==e&&(t||Ae(c._readableStreamController,e),!o&&d._readableStreamController._pendingPullIntos.length>0&&je(d._readableStreamController,0)),t&&o||a(void 0)},_errorSteps:()=>{l=!1}})}function S(){if(l)return s=!0,d(void 0);l=!0;const e=Be(o._readableStreamController);return null===e?p():y(e._view,!1),d(void 0)}function v(){if(l)return u=!0,d(void 0);l=!0;const e=Be(n._readableStreamController);return null===e?p():y(e._view,!0),d(void 0)}function w(o){if(f=!0,t=o,b){const o=ie([t,r]),n=br(e,o);a(n)}return _}function R(o){if(b=!0,r=o,f){const o=ie([t,r]),n=br(e,o);a(n)}return _}function T(){}return o=ur(T,S,w),n=ur(T,v,R),h(i),[o,n]}(e):function(e,t){const r=H(e);let o,n,a,i,l,s=!1,u=!1,f=!1,b=!1;const _=c((e=>{l=e}));function h(){if(s)return u=!0,d(void 0);s=!0;return K(r,{_chunkSteps:e=>{g((()=>{u=!1;const t=e,r=e;f||Gt(a._readableStreamController,t),b||Gt(i._readableStreamController,r),s=!1,u&&h()}))},_closeSteps:()=>{s=!1,f||Ut(a._readableStreamController),b||Ut(i._readableStreamController),f&&b||l(void 0)},_errorSteps:()=>{s=!1}}),d(void 0)}function p(t){if(f=!0,o=t,b){const t=ie([o,n]),r=br(e,t);l(r)}return _}function y(t){if(b=!0,n=t,f){const t=ie([o,n]),r=br(e,t);l(r)}return _}function S(){}return a=sr(S,h,p),i=sr(S,h,y),m(r._closedPromise,(e=>{Xt(a._readableStreamController,e),Xt(i._readableStreamController,e),f&&b||l(void 0)})),[a,i]}(e)}function rr(e,t,r){return F(e,r),r=>v(e,t,[r])}function or(e,t,r){return F(e,r),r=>v(e,t,[r])}function nr(e,t,r){return F(e,r),r=>S(e,t,[r])}function ar(e,t){if("bytes"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function ir(e,t){if("byob"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function lr(e,t){L(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,t.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:I(e,"First parameter");const r=Ve(t,"Second parameter"),o=function(e,t){L(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:x(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:rr(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:or(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:nr(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:ar(l,`${t} has member 'type' that`)}}(e,"First parameter");if(cr(this),"bytes"===o.type){if(void 0!==r.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n=()=>{},a=()=>d(void 0),i=()=>d(void 0);void 0!==t.start&&(n=()=>t.start(o)),void 0!==t.pull&&(a=()=>t.pull(o)),void 0!==t.cancel&&(i=e=>t.cancel(e));const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");ze(e,o,n,a,i,r,l)}(this,o,Ne(r,0))}else{const e=He(r);!function(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a=()=>{},i=()=>d(void 0),l=()=>d(void 0);void 0!==t.start&&(a=()=>t.start(n)),void 0!==t.pull&&(i=()=>t.pull(n)),void 0!==t.cancel&&(l=e=>t.cancel(e)),Zt(e,n,a,i,l,r,o)}(this,o,Ne(r,1),e)}}get locked(){if(!dr(this))throw mr("locked");return fr(this)}cancel(e){return dr(this)?fr(this)?f(new TypeError("Cannot cancel a stream that already has a reader")):br(this,e):f(mr("cancel"))}getReader(e){if(!dr(this))throw mr("getReader");return void 0===function(e,t){L(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:ir(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?H(this):Fe(this)}pipeThrough(e,t={}){if(!dr(this))throw mr("pipeThrough");$(e,1,"pipeThrough");const r=function(e,t){L(e,t);const r=null==e?void 0:e.readable;M(r,"readable","ReadableWritablePair"),N(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return M(o,"writable","ReadableWritablePair"),Ze(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=lr(t,"Second parameter");if(fr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(nt(r.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return y(Yt(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!dr(this))return f(mr("pipeTo"));if(void 0===e)return f("Parameter 1 is required in 'pipeTo'.");if(!ot(e))return f(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let r;try{r=lr(t,"Second parameter")}catch(e){return f(e)}return fr(this)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):nt(e)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Yt(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!dr(this))throw mr("tee");return ie(tr(this))}values(e){if(!dr(this))throw mr("values");return function(e,t){const r=H(e),o=new te(r,t),n=Object.create(re);return n._asyncIteratorImpl=o,n}(this,function(e,t){L(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,"First parameter").preventCancel)}}function sr(e,t,r,o=1,n=(()=>1)){const a=Object.create(ReadableStream.prototype);cr(a);return Zt(a,Object.create(ReadableStreamDefaultController.prototype),e,t,r,o,n),a}function ur(e,t,r){const o=Object.create(ReadableStream.prototype);cr(o);return ze(o,Object.create(ReadableByteStreamController.prototype),e,t,r,0,void 0),o}function cr(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function dr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof ReadableStream)}function fr(e){return void 0!==e._reader}function br(e,t){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return f(e._storedError);_r(e);const o=e._reader;void 0!==o&&Qe(o)&&(o._readIntoRequests.forEach((e=>{e._closeSteps(void 0)})),o._readIntoRequests=new w);return p(e._readableStreamController[j](t),r)}function _r(e){e._state="closed";const t=e._reader;void 0!==t&&(O(t),J(t)&&(t._readRequests.forEach((e=>{e._closeSteps()})),t._readRequests=new w))}function hr(e,t){e._state="errored",e._storedError=t;const r=e._reader;void 0!==r&&(W(r,t),J(r)?(r._readRequests.forEach((e=>{e._errorSteps(t)})),r._readRequests=new w):(r._readIntoRequests.forEach((e=>{e._errorSteps(t)})),r._readIntoRequests=new w))}function mr(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function pr(e,t){L(e,t);const r=null==e?void 0:e.highWaterMark;return M(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Q(r)}}Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(ReadableStream.prototype,t.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof t.asyncIterator&&Object.defineProperty(ReadableStream.prototype,t.asyncIterator,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const yr=e=>e.byteLength;Object.defineProperty(yr,"name",{value:"size",configurable:!0});class ByteLengthQueuingStrategy{constructor(e){$(e,1,"ByteLengthQueuingStrategy"),e=pr(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Sr(this))throw gr("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!Sr(this))throw gr("size");return yr}}function gr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function Sr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,t.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const vr=()=>1;Object.defineProperty(vr,"name",{value:"size",configurable:!0});class CountQueuingStrategy{constructor(e){$(e,1,"CountQueuingStrategy"),e=pr(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!Rr(this))throw wr("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!Rr(this))throw wr("size");return vr}}function wr(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function Rr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof CountQueuingStrategy)}function Tr(e,t,r){return F(e,r),r=>v(e,t,[r])}function Cr(e,t,r){return F(e,r),r=>S(e,t,[r])}function Pr(e,t,r){return F(e,r),(r,o)=>v(e,t,[r,o])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,t.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=Ve(t,"Second parameter"),n=Ve(r,"Third parameter"),a=function(e,t){L(e,t);const r=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,a=null==e?void 0:e.transform,i=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:Tr(r,e,`${t} has member 'flush' that`),readableType:o,start:void 0===n?void 0:Cr(n,e,`${t} has member 'start' that`),transform:void 0===a?void 0:Pr(a,e,`${t} has member 'transform' that`),writableType:i}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");const i=Ne(n,0),l=He(n),s=Ne(o,1),u=He(o);let b;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return p(e._backpressureChangePromise,(()=>{const o=e._writable;if("erroring"===o._state)throw o._storedError;return Ar(r,t)}))}return Ar(r,t)}(e,t)}function s(t){return function(e,t){return Er(e,t),d(void 0)}(e,t)}function u(){return function(e){const t=e._readable,r=e._transformStreamController,o=r._flushAlgorithm();return kr(r),p(o,(()=>{if("errored"===t._state)throw t._storedError;Ut(t._readableStreamController)}),(r=>{throw Er(e,r),t._storedError}))}(e)}function c(){return function(e){return Or(e,!1),e._backpressureChangePromise}(e)}function f(t){return Wr(e,t),d(void 0)}e._writable=function(e,t,r,o,n=1,a=(()=>1)){const i=Object.create(WritableStream.prototype);return rt(i),vt(i,Object.create(WritableStreamDefaultController.prototype),e,t,r,o,n,a),i}(i,l,u,s,r,o),e._readable=sr(i,c,f,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Or(e,!0),e._transformStreamController=void 0}(this,c((e=>{b=e})),s,u,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o=e=>{try{return jr(r,e),d(void 0)}catch(e){return f(e)}},n=()=>d(void 0);void 0!==t.transform&&(o=e=>t.transform(e,r));void 0!==t.flush&&(n=()=>t.flush(r));!function(e,t,r,o){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o}(e,r,o,n)}(this,a),void 0!==a.start?b(a.start(this._transformStreamController)):b(void 0)}get readable(){if(!qr(this))throw Dr("readable");return this._readable}get writable(){if(!qr(this))throw Dr("writable");return this._writable}}function qr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof TransformStream)}function Er(e,t){Xt(e._readable._readableStreamController,t),Wr(e,t)}function Wr(e,t){kr(e._transformStreamController),Ct(e._writable._writableStreamController,t),e._backpressure&&Or(e,!1)}function Or(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(TransformStream.prototype,t.toStringTag,{value:"TransformStream",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!Br(this))throw zr("desiredSize");return Jt(this._controlledTransformStream._readable._readableStreamController)}enqueue(e){if(!Br(this))throw zr("enqueue");jr(this,e)}error(e){if(!Br(this))throw zr("error");var t;t=e,Er(this._controlledTransformStream,t)}terminate(){if(!Br(this))throw zr("terminate");!function(e){const t=e._controlledTransformStream;Ut(t._readable._readableStreamController);const r=new TypeError("TransformStream terminated");Wr(t,r)}(this)}}function Br(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof TransformStreamDefaultController)}function kr(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function jr(e,t){const r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!Kt(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{Gt(o,t)}catch(e){throw Wr(r,e),r._readable._storedError}(function(e){return!Ht(e)})(o)!==r._backpressure&&Or(r,!0)}function Ar(e,t){return p(e._transformAlgorithm(t),void 0,(t=>{throw Er(e._controlledTransformStream,t),t}))}function zr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function Dr(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof t.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,t.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});const Lr={ReadableStream:ReadableStream,ReadableStreamDefaultController:ReadableStreamDefaultController,ReadableByteStreamController:ReadableByteStreamController,ReadableStreamBYOBRequest:ReadableStreamBYOBRequest,ReadableStreamDefaultReader:ReadableStreamDefaultReader,ReadableStreamBYOBReader:ReadableStreamBYOBReader,WritableStream:WritableStream,WritableStreamDefaultController:WritableStreamDefaultController,WritableStreamDefaultWriter:WritableStreamDefaultWriter,ByteLengthQueuingStrategy:ByteLengthQueuingStrategy,CountQueuingStrategy:CountQueuingStrategy,TransformStream:TransformStream,TransformStreamDefaultController:TransformStreamDefaultController};if(void 0!==o)for(const e in Lr)Object.prototype.hasOwnProperty.call(Lr,e)&&Object.defineProperty(o,e,{value:Lr[e],writable:!0,configurable:!0});e.ByteLengthQueuingStrategy=ByteLengthQueuingStrategy,e.CountQueuingStrategy=CountQueuingStrategy,e.ReadableByteStreamController=ReadableByteStreamController,e.ReadableStream=ReadableStream,e.ReadableStreamBYOBReader=ReadableStreamBYOBReader,e.ReadableStreamBYOBRequest=ReadableStreamBYOBRequest,e.ReadableStreamDefaultController=ReadableStreamDefaultController,e.ReadableStreamDefaultReader=ReadableStreamDefaultReader,e.TransformStream=TransformStream,e.TransformStreamDefaultController=TransformStreamDefaultController,e.WritableStream=WritableStream,e.WritableStreamDefaultController=WritableStreamDefaultController,e.WritableStreamDefaultWriter=WritableStreamDefaultWriter,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=polyfill.es2018.min.js.map
